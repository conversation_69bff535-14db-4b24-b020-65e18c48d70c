#*********************************************************************************

"""
Backtesting Strategy logic for XAUUSD (M5) using Strategy1 approach.

Enhancements:
- Added concise docstrings for init and next.
- Inserted short explanatory block comments.
- Preserved existing functionality.
"""
#*********************************************************************************





#*********************************************************************************

# Import necessary libraries

#*********************************************************************************
import sys
import os
import importlib.util

# Helper function to dynamically load a module
def load_module(module_name, relative_path):
    module_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), relative_path)
    spec = importlib.util.spec_from_file_location(module_name, module_path)
    module = importlib.util.module_from_spec(spec)
    sys.modules[module_name] = module
    spec.loader.exec_module(module)
    return module

# Dynamically load external modules
function = load_module("function", "../function.py")
FVG = load_module("FVG", "../FVG.py")  # Updated path to parent directory
OrderBlock = load_module("OrderBlock", "../OrderBlock.py")
Bos_Choch = load_module("Bos_Choch", "../Bos_Choch.py")
LiquiditySweeps = load_module("LiquiditySweeps", "../LiquiditySweeps.py")
Chart_pattern = load_module("Chart_pattern", "../Chart_pattern.py")
InternalOrderBlock = load_module("InternalOrderBlock", "../InternalOrderBlock.py")
SupportResistanceSignalMTF = load_module("SupportResistanceSignalMTF", "../SupportResistanceSignalMTF.py")  # Add this line
main = load_module("main", "../main.py")









import time
import MetaTrader5 as mt5
import pandas as pd
from function import calculate_rsi, calculate_ema, calculate_atr, calculate_adx
from main import send_order, get_historical_data, shutdown_mt5
from backtesting import Strategy
from FVG import FVG
from OrderBlock import detect_order_blocks, OrderBlockConfig
from tabulate import tabulate
import pytz
from datetime import datetime, time
from datetime import time as dtime
from InternalOrderBlock import store_order_block, OrderBlock  # NEW IMPORT






#*********************************************************************************

# Implementation of Strategy1 for backtesting 

#*********************************************************************************

class Strategy1(Strategy):
    """
    Strategy1 extends the backtesting 'Strategy' base class to implement a
    multi-timeframe approach that combines EMA, ATR, ADX, and Fair Value Gap
    (FVG) detection. It also checks for internal order blocks before placing
    trades, allowing for more nuanced entries.
    """


    #**********************************************************************************

    # Initialization method

    #**********************************************************************************

    def init(self):
        """
        Initializes technical indicators and key parameters:
        
        - EMA50, EMA200: Exponential moving averages for identifying trend slopes.
        - ATR14: Average True Range for volatility-based stop loss.
        - internalOrderBlocks, fairValueGaps: Lists to store discovered support/resistance data.
        - is_trading_time: Boolean that allows or forbids trades based on time blocks.
        - stopLossATRMultiplier: Multiplier used with ATR to set dynamic stop losses.
        - spreadPips: The spread in pips to add or subtract from entry prices.
        - bullishChochCount, bearishChochCount: Track break of structure events (CHoCH).
        - is_backtest: Boolean toggle for lighter logic vs. live trading.
        - lot, leverage: Defines order size and leverage for trades.
        - minADX: Minimal ADX threshold for filtering weak-trend markets.
        """
        self.ema20 = self.I(lambda c: pd.Series(c).ewm(span=20).mean(), self.data.Close)
        self.ema50 = self.I(lambda c: pd.Series(c).ewm(span=50).mean(), self.data.Close)
        self.ema200 = self.I(lambda c: pd.Series(c).ewm(span=200).mean(), self.data.Close)
        # Pass high, low, and close to calculate ATR
        self.atr14 = self.I(lambda h, l, c: calculate_atr(pd.DataFrame({'high': h, 'low': l, 'close': c}), period=14),
                              self.data.High, self.data.Low, self.data.Close)
        # Initialize defaults
        self.internalOrderBlocks = []
        self.fairValueGaps = []
        
        self.is_trading_time = False
        
        self.stopLossATRMultiplier = 2.0  # Increased for tighter risk management
        self.spreadPips = 0.05
        self.bullishChochCount = 0
        self.bearishChochCount = 0
        # Use lightweight logic when backtesting
        self.is_backtest = True

        # NEW: Set lot size and leverage for backtesting trades
        self.lot = 0.2        # Ensure lot size is 0.2
        self.leverage = 50    # Use leverage set to 1:50
        self.minADX = 20  # NEW: Minimum ADX threshold

        








    #**********************************************************************************

    # Main method for each new bar/step in backtesting

    #**********************************************************************************

    def next(self):
        """
        Core method executed on each new bar during backtesting (when self.is_backtest is True).
        
        1. Resamples M5 data to higher timeframes (H1, H4) to detect retests.
        2. Checks recent bars for Fair Value Gaps (FVG).
        3. Uses daily SMAs (50, 200) to anchor overall trend (fast_trend).
        4. Employs EMA crossover (50, 200) plus ADX > self.minADX to confirm market strength.
        5. If no prior order blocks exist, creates one using store_order_block.
        6. Verifies trading time windows, open positions, and volatility before placing trades.
        7. Prints out or logs trades for user visibility.
        """
        if self.is_backtest:
            # Cache backtest data and normalize column names
            data_m5 = self.data.df.copy()
            data_m5.columns = [c.lower() for c in data_m5.columns]
            
            ema20_last = self.ema20[-1]
            ema50_last = self.ema50[-1]
            ema200_last = self.ema200[-1]
            # Ensure the 'time' column exists in the DataFrame
            if 'time' not in data_m5.columns:
                data_m5['time'] = data_m5.index  # Add 'time' column if missing
            
            # Assume we're using the M5 timeframe in backtest
            fast_isFiveMinuteTimeframe = True
            
            # Resample for 1H and 4H conditions
            data_1h = data_m5.resample('1H').agg({'high': 'max', 'low': 'min', 'close': 'last'})
            data_4h = data_m5.resample('4H').agg({'high': 'max', 'low': 'min', 'close': 'last'})
            if not data_1h.empty and not data_4h.empty:
                last_10_1h = data_1h.tail(10)
                last_10_4h = data_4h.tail(10)
                high1h = last_10_1h['high'].max()
                low1h = last_10_1h['low'].min()
                high4h = last_10_4h['high'].max()
                low4h = last_10_4h['low'].min()
                current_close = self.data.Close[-1]
                fast_ht_retest = ((current_close < high1h) and (current_close > low1h)) or \
                                 ((current_close < high4h) and (current_close > low4h))
            else:
                fast_ht_retest = False
            
            # Check volatility on a higher timeframe (e.g., H1)
            data_h1 = self.data.df.copy()
            data_h1.rename(columns={'High': 'high', 'Low': 'low', 'Close': 'close'}, inplace=True)
            data_h1 = data_h1.resample('1H').agg({'high': 'max', 'low': 'min', 'close': 'last'}).dropna()
            if not data_h1.empty:
                atr_h1 = calculate_atr(data_h1, period=14)
                atr_ema_h1 = atr_h1.ewm(span=20).mean()
                self.is_volatile_market = atr_h1.iloc[-1] > atr_ema_h1.iloc[-1]
            else:
                self.is_volatile_market = False





            # *********************************************************************************

            # Fair Value Gap (FVG) retest using last 70 bars

            # *********************************************************************************

            last_70 = data_m5.tail(70).rename(columns=lambda c: c.lower())
            if 'time' not in last_70.columns:
                last_70['time'] = last_70.index

            fast_fvg = False
            if not last_70.empty:
                fvg_result = FVG.fvg(last_70)
                fvg_data = pd.DataFrame({
                    "is_bull": fvg_result["is_bull"],
                    "is_bear": fvg_result["is_bear"],
                    "gap_top": fvg_result["gap_top"],
                    "gap_bottom": fvg_result["gap_bottom"]
                })
                if not fvg_data.empty:
                    current_close = self.data.Close[-1]
                    latest_gaps = fvg_data.tail(5)
                    for _, gap in latest_gaps.iterrows():
                        if gap["is_bull"] and gap["gap_bottom"] <= current_close <= gap["gap_top"]:
                            fast_fvg = True
                        if gap["is_bear"] and gap["gap_bottom"] <= current_close <= gap["gap_top"]:
                            fast_fvg = True
            





            # *********************************************************************************

            # Determine Fast trend (SMA50 vs SMA200)

            # *********************************************************************************

            data_d1 = data_m5.resample('1D').agg({'close': 'last'}).dropna()
            if len(data_d1) >= 20:
                # Use a combination of SMA and RSI for trend detection
                sma50 = data_d1['close'].rolling(window=50).mean()
                sma200 = data_d1['close'].rolling(window=200).mean()
                rsi = calculate_rsi(data_d1['close'], period=14)
                
                # Determine trend based on SMA crossover and RSI levels
                if sma50.iloc[-1] > sma200.iloc[-1] and rsi.iloc[-1] > 50:
                    fast_trend = "BULLISH"
                elif sma50.iloc[-1] < sma200.iloc[-1] and rsi.iloc[-1] < 50:
                    fast_trend = "BEARISH"
                else:
                    fast_trend = "NEUTRAL"
            else:
                if not data_d1.empty:
                    # Fallback to recent price action if insufficient data for SMA/RSI
                    recent_closes = data_d1['close']
                    if recent_closes.iloc[-1] > recent_closes.mean():
                        fast_trend = "BULLISH"
                    else:
                        fast_trend = "BEARISH"
                else:
                    fast_trend = "NEUTRAL"
            
            # Ensure trendDirection always gets assigned after fast_trend is calculated
            trendDirection = "BULLISH" if ema20_last > ema200_last else "BEARISH"
            




            # *********************************************************************************

            # Use EMAs for strong trend check
            
            # *********************************************************************************
                                                                                                                                                                                   
            fast_strong = self.ema50[-1] > self.ema200[-1]
            # Dummy volatility check – adjust as needed
            fast_low_vol = False if self.atr14[-1] <= self.atr14[-1] else True
            







            # **********************************************************************************

            # Calculate ADX

            # **********************************************************************************

            current_adx = calculate_adx(
                pd.DataFrame({
                    'high': self.data.High[-100:], 
                    'low': self.data.Low[-100:], 
                    'close': self.data.Close[-100:]
                }), period=14
            ).iloc[-1]
            # Only trade if ADX is above threshold
            if current_adx < self.minADX:
                return  # Skip trading if trend is weak








            # *********************************************************************************

            # Check trading time

            # *********************************************************************************
            
            if fast_isFiveMinuteTimeframe and (fast_ht_retest or fast_fvg):
                # Extract current bar timing in Indian timezone and update trading time condition
                india_tz = pytz.timezone("Asia/Kolkata")
                # Assuming the index is timezone-naive in UTC, localize then convert
                current_bar_time = self.data.df.index[-1]
                if current_bar_time.tzinfo is None:
                    current_bar_time = pytz.utc.localize(current_bar_time)
                current_bar_time = current_bar_time.astimezone(india_tz).time()
                morning_start = dtime(8, 30)
                morning_end = dtime(12, 30)
                evening_start = dtime(17, 30)
                evening_end = dtime(20, 30)
                
                if (morning_start <= current_bar_time <= morning_end) or (evening_start <= current_bar_time <= evening_end):
                    self.is_trading_time = True
                else:
                    self.is_trading_time = False
            
            






            # *********************************************************************************

            # Check for internal order blocks

            # *********************************************************************************
            
            if len(self.internalOrderBlocks) == 0:
                class DummyPivot:
                    def __init__(self, barIndex):
                        self.barIndex = barIndex
                parsedHighs = data_m5['high'].tolist()
                parsedLows = data_m5['low'].tolist()
                times = data_m5['time'].tolist() if 'time' in data_m5.columns else data_m5.index.tolist()
                pivot = DummyPivot(0)
                bias = 1 if fast_trend == "BULLISH" else -1
                store_order_block(pivot, len(parsedHighs), bias, parsedHighs, parsedLows, times, self.internalOrderBlocks)
        
            # Proceed if we have an order block, trading is allowed, and trend is strong with acceptable volatility
            open_trades = len(self.trades)  # or check self.position if needed
            table_data = []




            




            #**********************************************************************************

            # ---------------------- Order Block Logic ----------------------

            #**********************************************************************************
            
            if len(self.internalOrderBlocks) > 0 and self.is_trading_time and fast_strong and (not fast_low_vol) and (open_trades == 0) and self.is_volatile_market:
                orderBlock = self.internalOrderBlocks[0]
                rawATR = self.atr14[-1]
                dynamicStopLoss = max(abs(rawATR) * self.stopLossATRMultiplier, 1e-6)

                if trendDirection == "BULLISH" and self.data.Close[-1] < orderBlock.barLow and (open_trades == 0):
                    entryPrice = self.data.Close[-1] + self.spreadPips
                    stopLossPrice = entryPrice - dynamicStopLoss * 1.2  # Increased stop loss slightly
                    targetPrice = entryPrice + dynamicStopLoss * 3.4
                    fix = round(abs( entryPrice - stopLossPrice ) / 10)
                    table_data.append(["Buy Signal (Order Block)", entryPrice, stopLossPrice, targetPrice, current_bar_time,fix])
                    print(current_bar_time)
                    
                    self.buy(size=fix, sl=stopLossPrice, tp=targetPrice)

                if trendDirection == "BEARISH" and self.data.Close[-1] > orderBlock.barHigh and (open_trades == 0):
                    entryPrice = self.data.Close[-1] - self.spreadPips
                    stopLossPrice = entryPrice + dynamicStopLoss * 1.2  # Increased stop loss slightly
                    targetPrice = entryPrice - dynamicStopLoss * 3.4
                    fix = round(abs( entryPrice - stopLossPrice ) / 10)
                    table_data.append(["Sell Signal (Order Block)", entryPrice, stopLossPrice, targetPrice, current_bar_time,fix])
                    print(current_bar_time)
                    
                    self.sell(size=fix, sl=stopLossPrice, tp=targetPrice)
                

                

            if table_data:
                print(tabulate(table_data, headers=["Signal", "Entry", "SL", "TP","current_bar_time","Lot Size"], tablefmt="grid"))







                
            # *********************************************************************************
            
            # ---------------------- Fair Value Gap (FVG) Logic ----------------------

            # *********************************************************************************

            if not fvg_data.empty:
                self.fairValueGaps = []
                for _, gap in fvg_data.iterrows():
                    self.fairValueGaps.append({
                        'is_bull': gap['is_bull'],
                        'is_bear': gap['is_bear'],
                        'top': gap['gap_top'],
                        'bottom': gap['gap_bottom']
                    })

            # FVG-based trade logic
            if fast_fvg and self.is_trading_time and fast_strong and self.is_volatile_market:
                rawATR = self.atr14[-1]
                dynamicStopLoss = rawATR * self.stopLossATRMultiplier if rawATR > 0 else 1e-6
                dynamicStopLoss = max(abs(rawATR) * self.stopLossATRMultiplier, 1e-6)
                table_data = []

                if trendDirection == "BULLISH":
                    for eachFairValueGap in self.fairValueGaps:
                        if self.data.Close[-1] > eachFairValueGap['bottom'] and self.data.Close[-1] < eachFairValueGap['top'] and (open_trades == 0):
                            entryPrice = eachFairValueGap['bottom'] + self.spreadPips
                            stopLossPrice = entryPrice - dynamicStopLoss*1.2
                            targetPrice = entryPrice + dynamicStopLoss * 3.4
                            fix = round(abs( entryPrice - stopLossPrice ) / 10)
                            table_data.append(["Buy Signal (FVG)", entryPrice, stopLossPrice, targetPrice,current_bar_time,fix])
                            print(current_bar_time)
                            self.buy(size=1, sl=stopLossPrice, tp=targetPrice)
                
                if trendDirection == "BEARISH":
                    for eachFairValueGap in self.fairValueGaps:
                        if self.data.Close[-1] < eachFairValueGap['top'] and self.data.Close[-1] > eachFairValueGap['bottom']  and (open_trades == 0):
                            entryPrice = eachFairValueGap['top'] - self.spreadPips
                            stopLossPrice = entryPrice + dynamicStopLoss*1.2
                            targetPrice = entryPrice - dynamicStopLoss * 3.4
                            fix = round(abs( entryPrice - stopLossPrice ) / 10)
                            table_data.append(["Sell Signal (FVG)", entryPrice, stopLossPrice, targetPrice,current_bar_time,fix])
                            print(current_bar_time)
                            self.sell(size=1, sl=stopLossPrice, tp=targetPrice)

                
                if table_data:
                    print(tabulate(table_data, headers=["Signal", "Entry", "SL", "TP","Current bar time","Lot Size"], tablefmt="grid"))
        return


















#********************************************************************************

# Higher Timeframe Retest Logic

#********************************************************************************

def checkHigherTimeframeRetest() -> bool:
    """
    Checks 1H and 4H chart data to confirm price is within recent high-low range,
    indicating a potential retest. Returns True/False accordingly.
    """
    data_1h = get_historical_data("XAUUSD", mt5.TIMEFRAME_H1, bars=10)
    data_4h = get_historical_data("XAUUSD", mt5.TIMEFRAME_H4, bars=10)
    if data_1h.empty or data_4h.empty:
        return False
    high1h = data_1h['high'].max()
    low1h = data_1h['low'].min()
    high4h = data_4h['high'].max()
    low4h = data_4h['low'].min()
    current_close = get_historical_data("XAUUSD", mt5.TIMEFRAME_M5, bars=1)['close'].iloc[-1]
    retest1h = (current_close < high1h) and (current_close > low1h)
    retest4h = (current_close < high4h) and (current_close > low4h)
    return retest1h or retest4h










#********************************************************************************

# Fair Value Gap Retest Logic

#********************************************************************************

def checkFairValueGapRetest() -> bool:
    """
    Retrieves recent M5 data, scans for Fair Value Gaps (FVG), and checks if
    current price is within any discovered gap.
    """
    data = get_historical_data("XAUUSD", mt5.TIMEFRAME_M5, bars=10)
    if data.empty:
        return False
    fvg_result = FVG.fvg(data)
    fvg_data = pd.DataFrame({
        "is_bull": fvg_result["is_bull"],
        "is_bear": fvg_result["is_bear"],
        "gap_top": fvg_result["gap_top"],
        "gap_bottom": fvg_result["gap_bottom"]
    })
    if fvg_data.empty:
        return False
    current_close = data['close'].iloc[-1]
    latest_gaps = fvg_data.tail(5)
    for _, gap in latest_gaps.iterrows():
        if gap["is_bull"] and gap["gap_bottom"] <= current_close <= gap["gap_top"]:
            return True
        if gap["is_bear"] and gap["gap_bottom"] <= current_close <= gap["gap_top"]:
            return True
    return False

















#********************************************************************************

# Checks if current timeframe is M5

#********************************************************************************

def isFiveMinuteTimeframe() -> bool:
    current_timeframe = mt5.TIMEFRAME_M5
    return current_timeframe == mt5.TIMEFRAME_M5












#********************************************************************************

# Confirms if trend is strong using EMAs

#********************************************************************************

def isStrongTrend() -> bool:
    df = get_historical_data("XAUUSD", mt5.TIMEFRAME_M5, bars=300)
    if df.empty:
        return False
    ema50 = df['close'].ewm(span=20).mean().iloc[-1]
    ema200 = df['close'].ewm(span=200).mean().iloc[-1]
    return ema50 > ema200










#********************************************************************************

# Evaluates if volatility is low using ATR

#********************************************************************************

def isLowVolatility() -> bool:
    df = get_historical_data("XAUUSD", mt5.TIMEFRAME_M5, bars=300)
    if df.empty:
        return False
    atr14 = calculate_atr(df, period=14)
    atr_ema50 = atr14.ewm(span=20).mean()
    return atr14.iloc[-1] < atr_ema50.iloc[-1]








#********************************************************************************

# Determines higher timeframe trend using D1 data

#********************************************************************************

def getHigherTimeframeTrend() -> str:
    df = get_historical_data("XAUUSD", mt5.TIMEFRAME_D1, bars=300)
    if df.empty:
        return "BULLISH"
    sma50 = df['close'].rolling(20).mean().iloc[-1]
    sma200 = df['close'].rolling(200).mean().iloc[-1]
    return "BULLISH" if sma50 > sma200 else "BEARISH"