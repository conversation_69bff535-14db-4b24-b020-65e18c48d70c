"""
Improved Strategy3: Liquidity Zone Retest and Support/Resistance-Based Trading Strategy

Enhancements:
- Added ADX filter to trade only when a strong trend is confirmed (e.g., ADX > 25).
- Added RSI filter to reduce false signals (e.g., RSI > 50 for bullish, RSI < 50 for bearish).
- Improved liquidity zone and support/resistance level checks by verifying current price proximity.
- Improved risk management with clear dynamic stop loss and target calculations.
- Implements a trailing stop mechanism to adjust stop_loss to break even once half the target distance is reached.
- Executes only one trade per bar and provides enhanced logging for debugging.
- Adjusted reward-to-risk ratio to improve profitability.
- Added trend confirmation bonus using EMA50.
"""

#***************************
# Import necessary libraries
#***************************
import MetaTrader5 as mt5
import pandas as pd
from function import calculate_atr, calculate_adx
from main import get_historical_data
from backtesting import Strategy
from LiquiditySweeps import detect_liquidity_sweeps
from Bos_Choch import market_structure_fractal
from tabulate import tabulate
from datetime import time as dtime
import pytz

#***************************
# Implementation of the improved Strategy3 for backtesting
#***************************
class Strategy3(Strategy):
    """
    Strategy3 implements a liquidity zone retest and support/resistance-based trading strategy.
    Trades are taken when price retests a liquidity zone and breaks support or resistance levels,
    confirmed by a strong trend via ADX and RSI filters.
    """

    def compute_rsi(self, series: pd.Series, period: int = 14) -> pd.Series:
        # Simple RSI calculation without talib
        delta = series.diff()
        up = delta.clip(lower=0)
        down = -1 * delta.clip(upper=0)
        ema_up = up.ewm(span=period, adjust=False).mean()
        ema_down = down.ewm(span=period, adjust=False).mean()
        rs = ema_up / ema_down
        return 100 - (100 / (1 + rs))

    def init(self):
        """
        Initializes technical indicators and key parameters:
        - EMA20, EMA50: Shorter-term exponential moving averages for momentum confirmation.
        - ATR14: Average True Range for dynamic stop loss and target prices.
        - ADX14: Average Directional Index for trend strength confirmation.
        - RSI14: Relative Strength Index for additional trend confirmation.
        - is_trading_time: Boolean to track if the current time is within trading hours.
        """
        self.ema20 = self.I(lambda c: pd.Series(c).ewm(span=20).mean(), self.data.Close)
        self.ema50 = self.I(lambda c: pd.Series(c).ewm(span=200).mean(), self.data.Close)
        self.ema200 = self.I(lambda c: pd.Series(c).ewm(span=200).mean(), self.data.Close)
        self.atr14 = self.I(lambda h, l, c: calculate_atr(pd.DataFrame({'high': h, 'low': l, 'close': c}), period=14),
                              self.data.High, self.data.Low, self.data.Close)
        self.adx14 = self.I(lambda h, l, c: calculate_adx(pd.DataFrame({'high': h, 'low': l, 'close': c}), period=14),
                              self.data.High, self.data.Low, self.data.Close)
        self.rsi14 = self.I(lambda c: self.compute_rsi(pd.Series(c), period=14), self.data.Close)
        self.volume = self.I(lambda v: pd.Series(v), self.data.Volume)
        self.liquidity_zones = []
        self.support_resistance_levels = []
        self.is_trading_time = False  # Initialize trading time flag
        


    def next(self):
        """
        Main logic executed on each new bar:
        - Simplifies conditions to ensure trades are executed.
        - Uses EMA crossovers for trend confirmation.
        """
        # Ensure trades are only taken during allowed trading hours
        india_tz = pytz.timezone("Asia/Kolkata")
                # Assuming the index is timezone-naive in UTC, localize then convert
        current_bar_time = self.data.df.index[-1]
        if current_bar_time.tzinfo is None:
            current_bar_time = pytz.utc.localize(current_bar_time)
        current_bar_time = current_bar_time.astimezone(india_tz).time()
        morning_start = dtime(8, 30)
        morning_end = dtime(12, 30)
        evening_start = dtime(17, 30)
        evening_end = dtime(20, 30)
        
        if (morning_start <= current_bar_time <= morning_end) or (evening_start <= current_bar_time <= evening_end):
            self.is_trading_time = True
        else:
            self.is_trading_time = False
            

        ema20_last = self.ema20[-1]
        ema50_last = self.ema50[-1]
        atr14_last = self.atr14[-1]
        current_price = self.data.Close[-1]
        open_trades = len(self.trades) 










        # Check volatility on a higher timeframe (e.g., H1)
        data_h1 = self.data.df.copy()
        data_h1.rename(columns={'High': 'high', 'Low': 'low', 'Close': 'close'}, inplace=True)
        data_h1 = data_h1.resample('1H').agg({'high': 'max', 'low': 'min', 'close': 'last'}).dropna()
        if not data_h1.empty:
            atr_h1 = calculate_atr(data_h1, period=14)
            atr_ema_h1 = atr_h1.ewm(span=20).mean()
            self.is_volatile_market = atr_h1.iloc[-1] > atr_ema_h1.iloc[-1]
        else:
            self.is_volatile_market = False


        # Simplify momentum confirmation using EMA20 and EMA50
        trend_direction = "BULLISH" if ema20_last > ema50_last else "BEARISH"

        # # Additional trend filter: for bullish signals, EMA20 must be above EMA200; for bearish signals, EMA20 below EMA200.
        if trend_direction == "BULLISH" and ema20_last < self.ema200[-1]:
            return
        if trend_direction == "BEARISH" and ema20_last > self.ema200[-1]:
            return

        # Ensure the DataFrame has the required columns
        if not hasattr(self, '_processed_data'):
            self._processed_data = self.data.df.copy()
            self._processed_data.rename(columns={'Open': 'open', 'High': 'high', 'Low': 'low', 'Close': 'close'}, inplace=True)
            self._processed_data['time'] = self._processed_data.index  # Ensure 'time' column exists

        # Detect liquidity zones (only once per bar)
        if not hasattr(self, '_liquidity_data') or self._liquidity_data.index[-1] != self.data.index[-1]:
            self._liquidity_data = detect_liquidity_sweeps(self._processed_data)
            if not self._liquidity_data.empty:
                self.liquidity_zones = self._liquidity_data.to_dict('records')
            else:
                self.liquidity_zones = []

        # Detect support/resistance levels (only once per bar)
        if not hasattr(self, '_support_resistance_data') or self._support_resistance_data.index[-1] != self.data.index[-1]:
            self._support_resistance_data = market_structure_fractal(
                self._processed_data, length=5, show_support=True, show_resistance=True
            )
            if not self._support_resistance_data.empty:
                self.support_resistance_levels = self._support_resistance_data.tail(5).to_dict('records')
            else:
                self.support_resistance_levels = []

                # Implement fallback trade logic only:
        table_data = []  # Initialize trade log table
        last_5_closes = self.data.Close[-5:]
        any_close_below_lower = False
        any_close_above_upper = False
        zone_lower = None
        zone_upper = None
        if self.liquidity_zones:
            sample_zone = self.liquidity_zones[0]
            zone_lower = sample_zone.get('low', None)
            zone_upper = sample_zone.get('high', None)
            if zone_lower is not None:
                any_close_below_lower = any(close < zone_lower for close in last_5_closes)
            if zone_upper is not None:
                any_close_above_upper = any(close > zone_upper for close in last_5_closes)

        # Implement fallback trade logic only:
        if not self.liquidity_zones and not self.support_resistance_levels:
            return
        if self.atr14[-1] is None:
            return

        if trend_direction == "BULLISH" and zone_lower is not None and current_price < zone_lower and any_close_below_lower and self.is_trading_time and (open_trades == 0) and self.adx14[-1] < 55 and self.is_volatile_market:
            entry_price = current_price
            # Zone-Bonus: adjust SL/TP for BUY to allow a higher profit target
            if (zone_lower - current_price) < (0.2 * atr14_last):
                stop_loss = entry_price - 2.3 * atr14_last
                target_price = entry_price + 5.1 * atr14_last
            else:
                stop_loss = entry_price - 2.5 * atr14_last
                target_price = entry_price + 5.4 * atr14_last
            self.buy(size=1, sl=stop_loss, tp=target_price)
            table_data.append(["Buy Signal (Liquidity Zone)", entry_price, stop_loss, target_price])
            # print(f"Buy Signal: Entry={entry_price}, SL={stop_loss}, TP={target_price}")
        elif trend_direction == "BEARISH" and zone_upper is not None and current_price > zone_upper and any_close_above_upper and self.is_trading_time and (open_trades == 0) and self.adx14[-1] < 55 and self.is_volatile_market:
            entry_price = current_price
            # Zone-Bonus: adjust SL/TP for SELL to allow a higher profit target
            if (current_price - zone_upper) < (0.2 * atr14_last):
                stop_loss = entry_price + 2.3 * atr14_last
                target_price = entry_price - 5.1 * atr14_last
            else:
                stop_loss = entry_price + 2.5 * atr14_last
                target_price = entry_price - 5.4 * atr14_last
            self.sell(size=1, sl=stop_loss, tp=target_price)
            table_data.append(["Sell Signal (Liquidity Zone)", entry_price, stop_loss, target_price])
            # print(f"Sell Signal: Entry={entry_price}, SL={stop_loss}, TP={target_price}")
        
        if table_data:
            print(tabulate(table_data, headers=["Signal", "Entry", "SL", "TP"], tablefmt="grid"))
        
       
    def calculate_trade_parameters(self, entry_price, atr14_last, direction):
        """
        Ensure stop_loss and target_price always satisfy:
         - BUY: SL < entry_price < TP
         - SELL: TP < entry_price < SL
        """
        safe_atr = abs(atr14_last)
        if direction == "BUY":
            stop_loss = entry_price - 2 * safe_atr
            target_price = entry_price + 6 * safe_atr
            if not (stop_loss < entry_price < target_price):
                return None, None
        else:
            stop_loss = entry_price + 2 * safe_atr
            target_price = entry_price - 6 * safe_atr
            if not (target_price < entry_price < stop_loss):
                return None, None

        return stop_loss, target_price

    def run(self):
        """
        Runs the strategy and manages trades with trailing stop mechanism.
        """
        super().run()