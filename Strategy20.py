import MetaTrader5 as mt5
import pandas as pd
from datetime import datetime, timedelta, time

# Connect to MetaTrader 10
if not mt5.initialize():
    print("initialize() failed")
    quit()

symbol = "XAUUSD.0"

# Get last 10 days of 1-min data
utc_to = datetime.now()
utc_from = utc_to - timedelta(days=1)
rates = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_M1, utc_from, utc_to)
mt5.shutdown()

if rates is None or len(rates) == 0:
    print("No data retrieved from MT5.")
    quit()

# Prepare DataFrame
df = pd.DataFrame(rates)
df['Datetime'] = pd.to_datetime(df['time'], unit='s')
df = df.set_index('Datetime')

# Define time windows
time_windows = [
    
    (time(11, 0), time(15, 0)),
    (time(3, 0), time(6, 0))
]

results = []

for date, day_df in df.groupby(df.index.date):
    for start, end in time_windows:
        # Filter by time window
        mask = (day_df.index.time >= start) & (day_df.index.time <= end)
        window_df = day_df[mask]
        if window_df.empty:
            continue

        # Find all multiples of 10 in price range for the day
        min_price = int(window_df['low'].min() // 10 * 10)
        max_price = int(window_df['high'].max() // 10 * 10) + 10
        levels = list(range(min_price, max_price + 1, 10))

        # For each level, check for crossings
        for level in levels:
            prev_close = None
            prev_idx = None
            for idx, row in window_df.iterrows():
                close = row['close']
                if prev_close is not None:
                    # Upward cross
                    if prev_close < level <= close:
                        next_level = level + 10
                        # Search for next crossing of next_level after this bar
                        crossed = False
                        for idx2, row2 in window_df.loc[idx:].iterrows():
                            close2 = row2['close']
                            if close < next_level <= close2:
                                time_diff = (idx2 - idx).total_seconds() / 60.0  # in minutes
                                results.append({
                                    'Date': date,
                                    'Window': f'{start.strftime("%H:%M")}-{end.strftime("%H:%M")}',
                                    'First_Crossed': level,
                                    'Direction': 'Up',
                                    'Time_First_Cross': idx.time(),
                                    'Close_First_Cross': close,
                                    'Next_Level_Crossed': next_level,
                                    'Time_Next_Cross': idx2.time(),
                                    'Close_Next_Cross': close2,
                                    'Minutes_Between_Crosses': time_diff
                                })
                                crossed = True
                                break
                        if crossed:
                            break  # Only first valid sequence per level per window
                    # Downward cross
                    if prev_close > level >= close:
                        prev_level = level - 10
                        # Search for next crossing of prev_level after this bar
                        crossed = False
                        for idx2, row2 in window_df.loc[idx:].iterrows():
                            close2 = row2['close']
                            if close > prev_level >= close2:
                                time_diff = (idx2 - idx).total_seconds() / 60.0  # in minutes
                                results.append({
                                    'Date': date,
                                    'Window': f'{start.strftime("%H:%M")}-{end.strftime("%H:%M")}',
                                    'First_Crossed': level,
                                    'Direction': 'Down',
                                    'Time_First_Cross': idx.time(),
                                    'Close_First_Cross': close,
                                    'Next_Level_Crossed': prev_level,
                                    'Time_Next_Cross': idx2.time(),
                                    'Close_Next_Cross': close2,
                                    'Minutes_Between_Crosses': time_diff
                                })
                                crossed = True
                                break
                        if crossed:
                            break  # Only first valid sequence per level per window
                prev_close = close
                prev_idx = idx

# Convert results to DataFrame and print
results_df = pd.DataFrame(results)
if not results_df.empty:
    print(results_df.head(30))  # Show first 30 crossings for brevity
    results_df.to_csv('xauusd_1000pip_crossings_nextlevel.csv', index=False)
else:
    print("No valid double crossings found in the specified windows.")