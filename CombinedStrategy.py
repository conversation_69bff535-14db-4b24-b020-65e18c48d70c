import MetaTrader5 as mt5
import time
import datetime
from tabulate import tabulate
import math
import pandas as pd
from datetime import timedelta
import numpy as np

# Add trade_history to track trades
trade_history = []

# Add trade_pnl_history to track all closed trades with their PnL and signal
trade_pnl_history = []

symbol = "XAUUSD"
pip = 0.0001  # Define pip size

# Helper functions (simplified implementations)
def get_current_price():
    tick = mt5.symbol_info_tick(symbol)
    return tick.ask if tick else 0

def log_trade(action, lot, price, position_id=None):
    """Log trade details and include position ID."""
    trade_history.append({
        "time": time.strftime("%Y-%m-%d %H:%M:%S"),
        "action": action,
        "lot": lot,
        "price": price,
        "position_id": position_id
    })

def open_long(L, price, sl):
    # Calculate TP as 1.3 * distance from entry to SL
    sl_distance = abs(price - sl)/1.25
    tp = price + 1.45 * sl_distance
    sl = price - sl_distance
    req = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": L,
        "type": mt5.ORDER_TYPE_BUY,
        "price": price,
        "sl": sl,
        "tp": tp,
        "deviation": 1,
        "magic": 234000,
        "comment": "Open Long",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }
    res = mt5.order_send(req)
    print("Open Long:", res)
    if res and res.retcode == mt5.TRADE_RETCODE_DONE:
        log_trade("OPEN_LONG", L, price, res.order)
        return res.order
    return None

def open_short(L, price, sl):
    # Calculate TP as 1.3 * distance from entry to SL
    sl_distance = abs(price - sl)/1.25
    tp = price - 1.45 * sl_distance
    sl = price + sl_distance
    req = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": L,
        "type": mt5.ORDER_TYPE_SELL,
        "price": price,
        "sl": sl,
        "tp": tp,
        "deviation": 1,
        "magic": 234000,
        "comment": "Open Short",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }
    res = mt5.order_send(req)
    print("Open Short:", res)
    if res and res.retcode == mt5.TRADE_RETCODE_DONE:
        log_trade("OPEN_SHORT", L, price, res.order)
        return res.order
    return None

def close_long(L, price=None, position_id=None):
    """Close a specific long position."""
    
    tick = mt5.symbol_info_tick(symbol)
    price = price if price is not None else (tick.bid if tick else 0)
    req = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": L,
        "type": mt5.ORDER_TYPE_SELL,
        "position": position_id,
        "price": price,
        "deviation": 1,
        "magic": 234000,
        "comment": "Close Long",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }
    res = mt5.order_send(req)
    if res is None:
        print("Close Long: order_send returned None")
        return
    print("Close Long:", res)

def close_short(L, price=None, position_id=None):
    """Close a specific short position."""
    tick = mt5.symbol_info_tick(symbol)
    price = price if price is not None else (tick.ask if tick else 0)
    req = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": L,
        "type": mt5.ORDER_TYPE_BUY,
        "position": position_id,
        "price": price,
        "deviation": 1,
        "magic": 234000,
        "comment": "Close Short",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }
    res = mt5.order_send(req)
    if res is None:
        print("Close Short: order_send returned None")
        return
    print("Close Short:", res)

def close_all_positions_and_delete_pending():
    positions = mt5.positions_get(symbol=symbol)
    if positions:
        for pos in positions:
            if pos.type == 0:  # long
                close_long(pos.volume, position_id=pos.ticket)
            elif pos.type == 1:  # short
                close_short(pos.volume, position_id=pos.ticket)
    orders = mt5.orders_get(symbol=symbol)
    if orders:
        for order in orders:
            req = {
                "action": mt5.TRADE_ACTION_REMOVE,
                "order": order.ticket,
            }
            mt5.order_send(req)
            print("Deleted pending order:", order.ticket)

def print_trade_table(outcome, entry_price, exit_price, profit_pips, next_lot, profit_count=None, loss_count=None):
    headers = ["Outcome", "Entry Price", "Exit Price", "Profit (pips)", "Next Lot Size", "Max Lot Profit Count", "Max Lot Loss Count"]
    data = [[outcome, entry_price, exit_price, profit_pips, next_lot, profit_count, loss_count]]
    print(tabulate(data, headers=headers, tablefmt="fancy_grid"))

def print_pnl_table():
    """Print a table of all closed trades with their PnL, signal, and count. Also print net PnL after tax."""
    if not trade_pnl_history:
        print("No closed trades yet.")
        return
    headers = ["#", "Signal", "PnL", "Lot Size", "Tax", "Net PnL (after tax)"]
    data = []
    net_pnl = 0
    for i, t in enumerate(trade_pnl_history):
        lot = t.get("lot_size", 0.01)  # fallback to 0.01 if not present
        tax = round(lot * 10, 2)
        net = t["pnl"] - tax
        net_pnl += net
        data.append([i+1, t["signal"], t["pnl"], lot, tax, round(net, 2)])
    print(tabulate(data, headers=headers, tablefmt="fancy_grid"))
    profit_count = sum(1 for t in trade_pnl_history if t["pnl"] > 0)
    loss_count = sum(1 for t in trade_pnl_history if t["pnl"] <= 0)
    print(f"Total Trades: {len(trade_pnl_history)} | Profits: {profit_count} | Losses: {loss_count} | Net PnL (after tax): {net_pnl:.2f}")

def get_latest_closed_trade_pnl(position_id):
    """Fetch the PnL of the latest closed trade using history."""
    deals = mt5.history_deals_get(position=position_id)
    if deals:
        latest_deal = max(deals, key=lambda deal: deal.time)  # Get the most recent deal
        pnl = latest_deal.profit  # Use the profit attribute from the deal
        return pnl
    return None

def calculate_ema(prices, period=9):
    """Calculate EMA for the given prices and period."""
    prices = np.array(prices)
    if len(prices) < period:
        return None
    weights = np.exp(np.linspace(-1., 0., period))
    weights /= weights.sum()
    a = np.convolve(prices, weights, mode='valid')
    return a[-1]

def calculate_macd(prices, fast=12, slow=26, signal=9):
    """Calculate MACD and return signal."""
    if len(prices) < slow + signal:
        return None
    ema_fast = pd.Series(prices).ewm(span=fast, adjust=False).mean()
    ema_slow = pd.Series(prices).ewm(span=slow, adjust=False).mean()
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signal, adjust=False).mean()
    if macd_line.iloc[-1] > signal_line.iloc[-1] and macd_line.iloc[-2] <= signal_line.iloc[-2]:
        return "BUY"
    elif macd_line.iloc[-1] < signal_line.iloc[-1] and macd_line.iloc[-2] >= signal_line.iloc[-2]:
        return "SELL"
    return None

def calculate_adx(high, low, close, period=14):
    """Calculate ADX and return signal."""
    if len(close) < period + 1:
        return None
    df = pd.DataFrame({'high': high, 'low': low, 'close': close})
    df['tr'] = np.maximum(df['high'] - df['low'], 
                          np.maximum(abs(df['high'] - df['close'].shift()), abs(df['low'] - df['close'].shift())))
    df['+dm'] = np.where((df['high'] - df['high'].shift()) > (df['low'].shift() - df['low']), 
                         np.maximum(df['high'] - df['high'].shift(), 0), 0)
    df['-dm'] = np.where((df['low'].shift() - df['low']) > (df['high'] - df['high'].shift()), 
                         np.maximum(df['low'].shift() - df['low'], 0), 0)
    tr14 = df['tr'].rolling(window=period).sum()
    plus_dm14 = df['+dm'].rolling(window=period).sum()
    minus_dm14 = df['-dm'].rolling(window=period).sum()
    plus_di14 = 100 * (plus_dm14 / tr14)
    minus_di14 = 100 * (minus_dm14 / tr14)
    dx = 100 * abs(plus_di14 - minus_di14) / (plus_di14 + minus_di14)
    adx = dx.rolling(window=period).mean()
    if plus_di14.iloc[-1] > minus_di14.iloc[-1] and adx.iloc[-1] > 20:
        return "BUY"
    elif minus_di14.iloc[-1] > plus_di14.iloc[-1] and adx.iloc[-1] > 20:
        return "SELL"
    return None

def calculate_atr(high, low, close, period=14):
    """Calculate ATR (not a direct signal, but useful for SL/TP)."""
    df = pd.DataFrame({'high': high, 'low': low, 'close': close})
    df['tr'] = np.maximum(df['high'] - df['low'], 
                          np.maximum(abs(df['high'] - df['close'].shift()), abs(df['low'] - df['close'].shift())))
    atr = df['tr'].rolling(window=period).mean()
    return atr.iloc[-1]

def calculate_rsi(prices, period=14):
    """Calculate RSI and return signal."""
    if len(prices) < period + 1:
        return None
    delta = np.diff(prices)
    up = delta.clip(min=0)
    down = -1 * delta.clip(max=0)
    ma_up = pd.Series(up).rolling(window=period).mean()
    ma_down = pd.Series(down).rolling(window=period).mean()
    rs = ma_up / ma_down
    rsi = 100 - (100 / (1 + rs))
    if rsi.iloc[-1] < 30:
        return "BUY"
    elif rsi.iloc[-1] > 70:
        return "SELL"
    return None

def calculate_bollinger_bands(prices, period=20, num_std=2):
    if len(prices) < period:
        return None, None, None
    series = pd.Series(prices)
    sma = series.rolling(window=period).mean()
    std = series.rolling(window=period).std()
    upper = sma + num_std * std
    lower = sma - num_std * std
    return sma.iloc[-1], upper.iloc[-1], lower.iloc[-1]

def calculate_stochastic_oscillator(high, low, close, k_period=14, d_period=3):
    if len(close) < k_period + d_period:
        return None
    low_min = pd.Series(low).rolling(window=k_period).min()
    high_max = pd.Series(high).rolling(window=k_period).max()
    k = 100 * (pd.Series(close) - low_min) / (high_max - low_min)
    d = k.rolling(window=d_period).mean()
    if k.iloc[-1] < 20 and d.iloc[-1] < 20:
        return "BUY"
    elif k.iloc[-1] > 80 and d.iloc[-1] > 80:
        return "SELL"
    return None

def calculate_parabolic_sar(high, low, close, af=0.02, max_af=0.2):
    if len(close) < 2:
        return None
    sar = [low[0]]
    long = True
    ep = high[0]
    af_val = af
    for i in range(1, len(close)):
        prev_sar = sar[-1]
        if long:
            sar.append(prev_sar + af_val * (ep - prev_sar))
            if high[i] > ep:
                ep = high[i]
                af_val = min(af_val + af, max_af)
            if close[i] < sar[-1]:
                long = False
                sar[-1] = ep
                ep = low[i]
                af_val = af
        else:
            sar.append(prev_sar + af_val * (ep - prev_sar))
            if low[i] < ep:
                ep = low[i]
                af_val = min(af_val + af, max_af)
            if close[i] > sar[-1]:
                long = True
                sar[-1] = ep
                ep = high[i]
                af_val = af
    if close[-1] > sar[-1]:
        return "BUY"
    elif close[-1] < sar[-1]:
        return "SELL"
    return None

def calculate_cci(high, low, close, period=20):
    if len(close) < period:
        return None
    tp = (np.array(high) + np.array(low) + np.array(close)) / 3
    ma = pd.Series(tp).rolling(window=period).mean()
    md = pd.Series(tp).rolling(window=period).apply(lambda x: np.mean(np.abs(x - np.mean(x))))
    cci = (tp - ma) / (0.015 * md)
    if cci.iloc[-1] > 100:
        return "BUY"
    elif cci.iloc[-1] < -100:
        return "SELL"
    return None

def calculate_williams_r(high, low, close, period=14):
    if len(close) < period:
        return None
    highest_high = pd.Series(high).rolling(window=period).max()
    lowest_low = pd.Series(low).rolling(window=period).min()
    r = -100 * (highest_high - pd.Series(close)) / (highest_high - lowest_low)
    if r.iloc[-1] < -80:
        return "BUY"
    elif r.iloc[-1] > -20:
        return "SELL"
    return None

def calculate_mfi(high, low, close, volume, period=14):
    if len(close) < period + 1:
        return None
    tp = (np.array(high) + np.array(low) + np.array(close)) / 3
    raw_mf = tp * np.array(volume)
    pos_mf = np.where(tp[1:] > tp[:-1], raw_mf[1:], 0)
    neg_mf = np.where(tp[1:] < tp[:-1], raw_mf[1:], 0)
    pos_mf_sum = pd.Series(pos_mf).rolling(window=period).sum()
    neg_mf_sum = pd.Series(neg_mf).rolling(window=period).sum()
    mfi = 100 - (100 / (1 + (pos_mf_sum / neg_mf_sum)))
    if mfi.iloc[-1] < 20:
        return "BUY"
    elif mfi.iloc[-1] > 80:
        return "SELL"
    return None

def calculate_obv(close, volume):
    if len(close) < 2:
        return None
    obv = [0]
    for i in range(1, len(close)):
        if close[i] > close[i-1]:
            obv.append(obv[-1] + volume[i])
        elif close[i] < close[i-1]:
            obv.append(obv[-1] - volume[i])
        else:
            obv.append(obv[-1])
    # Simple signal: OBV rising = BUY, falling = SELL
    if obv[-1] > obv[-2]:
        return "BUY"
    elif obv[-1] < obv[-2]:
        return "SELL"
    return None

def calculate_roc(prices, period=12):
    if len(prices) < period + 1:
        return None
    roc = ((prices[-1] - prices[-period-1]) / prices[-period-1]) * 100
    if roc > 0:
        return "BUY"
    elif roc < 0:
        return "SELL"
    return None

def is_ranging_market(close, adx, bb_upper, bb_lower):
    # Ranging if ADX is low and Bollinger Band width is narrow
    if adx is not None and adx < 20:
        bb_width = bb_upper - bb_lower if bb_upper is not None and bb_lower is not None else None
        if bb_width is not None and bb_width / close[-1] < 0.01:
            return True
    return False

def get_latest_ohlc_data(symbol, seconds_count=60):
    """Fetch latest OHLC data for the given symbol and seconds count."""
    utc_now = datetime.datetime.utcnow()
    utc_from = utc_now - timedelta(seconds=seconds_count)
    ticks = mt5.copy_ticks_range(symbol, utc_from, utc_now, mt5.COPY_TICKS_ALL)
    if ticks is None or len(ticks) == 0:
        return None
    data = pd.DataFrame(ticks)
    if 'time_msc' in data.columns:
        data['time'] = pd.to_datetime(data['time_msc'], unit='ms')
    else:
        data['time'] = pd.to_datetime(data['time'], unit='s')
    data.set_index('time', inplace=True)
    price = data['bid'] if 'bid' in data.columns else data['last']
    ohlc = price.resample("1S").ohlc().dropna()
    ohlc['close'] = ohlc['close'].fillna(method='ffill')
    ohlc['high'] = ohlc['high'].fillna(ohlc['close'])
    ohlc['low'] = ohlc['low'].fillna(ohlc['close'])
    ohlc['open'] = ohlc['open'].fillna(ohlc['close'])
    return ohlc

def get_1min_ohlc(symbol, bars=2):
    """
    Fetch last N 1-minute OHLC bars for the symbol.
    Always fetches the most recent closed candles (does not include the currently forming candle).
    Uses server time to avoid local clock mismatch.
    """
    # Get the latest closed candle's end time from server
    tick = mt5.symbol_info_tick(symbol)
    if not tick:
        return None
    # Find the start of the current minute (server time)
    server_time = datetime.datetime.utcfromtimestamp(tick.time)
    minute_start = server_time.replace(second=0, microsecond=0)
    # To ensure we get the last N closed candles, use mt5.copy_rates_from_pos with position -bars (from the last closed candle)
    rates = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_M1, 0, bars)
    if rates is None or len(rates) < bars:
        return None
    df = pd.DataFrame(rates)
    df['time'] = pd.to_datetime(df['time'], unit='s')
    df.set_index('time', inplace=True)
    return df

# Track per-indicator accuracy
indicator_stats = {
    "MACD": {"correct": 0, "total": 0},
    "RSI": {"correct": 0, "total": 0},
    "Bollinger": {"correct": 0, "total": 0},
    "ADX": {"correct": 0, "total": 0},
    "EMA": {"correct": 0, "total": 0},
    "Stochastic": {"correct": 0, "total": 0},
    "ParabolicSAR": {"correct": 0, "total": 0},
    "CCI": {"correct": 0, "total": 0},
    "WilliamsR": {"correct": 0, "total": 0},
    "MFI": {"correct": 0, "total": 0},
    "OBV": {"correct": 0, "total": 0},
    "ROC": {"correct": 0, "total": 0},
}

def print_accuracy_table():
    """Print a table showing accuracy for each indicator."""
    headers = ["Indicator", "Correct", "Total", "Accuracy (%)"]
    data = []
    for name, stats in indicator_stats.items():
        total = stats["total"]
        correct = stats["correct"]
        acc = (correct / total * 100) if total > 0 else 0
        data.append([name, correct, total, f"{acc:.2f}"])
    print(tabulate(data, headers=headers, tablefmt="fancy_grid"))

def get_consensus_signal(symbol):
    """Aggregate signals from all indicators with weights and return the consensus signal. Ignore if ranging."""
    ohlc = get_latest_ohlc_data(symbol, 60)
    if ohlc is None or len(ohlc) < 30:
        print("Not enough OHLC data for indicators.")
        return None

    close = ohlc['close'].values
    high = ohlc['high'].values
    low = ohlc['low'].values
    volume = ohlc['close'].size * [1] if 'volume' not in ohlc.columns else ohlc['volume'].fillna(0).values

    # Calculate indicators and their signals
    signals = []
    weights = []
    indicator_names = []

    # MACD (weight 3)
    macd_signal = calculate_macd(close)
    signals.append(macd_signal)
    weights.append(3)
    indicator_names.append("MACD")

    # RSI (weight 2)
    rsi_signal = calculate_rsi(close)
    signals.append(rsi_signal)
    weights.append(2)
    indicator_names.append("RSI")

    # Bollinger Bands (weight 2)
    sma, bb_upper, bb_lower = calculate_bollinger_bands(close)
    bb_signal = None
    if bb_upper is not None and bb_lower is not None:
        if close[-1] > bb_upper:
            bb_signal = "SELL"
        elif close[-1] < bb_lower:
            bb_signal = "BUY"
    signals.append(bb_signal)
    weights.append(2)
    indicator_names.append("Bollinger")

    # ADX (weight 2)
    adx_signal = calculate_adx(high, low, close)
    adx_val = None
    if len(close) >= 15:
        # Calculate ADX value for ranging detection
        df = pd.DataFrame({'high': high, 'low': low, 'close': close})
        df['tr'] = np.maximum(df['high'] - df['low'], 
                              np.maximum(abs(df['high'] - df['close'].shift()), abs(df['low'] - df['close'].shift())))
        tr14 = df['tr'].rolling(window=14).sum()
        plus_dm14 = np.where((df['high'] - df['high'].shift()) > (df['low'].shift() - df['low']),
                             np.maximum(df['high'] - df['high'].shift(), 0), 0)
        minus_dm14 = np.where((df['low'].shift() - df['low']) > (df['high'] - df['high'].shift()),
                              np.maximum(df['low'].shift() - df['low'], 0), 0)
        plus_di14 = 100 * (pd.Series(plus_dm14).rolling(window=14).sum() / tr14)
        minus_di14 = 100 * (pd.Series(minus_dm14).rolling(window=14).sum() / tr14)
        dx = 100 * abs(plus_di14 - minus_di14) / (plus_di14 + minus_di14)
        adx_val = dx.rolling(window=14).mean().iloc[-1]
    signals.append(adx_signal)
    weights.append(2)
    indicator_names.append("ADX")

    # Moving Average (EMA, weight 1)
    ema_signal = None
    ema9 = calculate_ema(close, period=9)
    if ema9 is not None:
        if close[-1] > ema9:
            ema_signal = "BUY"
        elif close[-1] < ema9:
            ema_signal = "SELL"
    signals.append(ema_signal)
    weights.append(1)
    indicator_names.append("EMA")

    # Stochastic Oscillator (weight 1)
    stoch_signal = calculate_stochastic_oscillator(high, low, close)
    signals.append(stoch_signal)
    weights.append(1)
    indicator_names.append("Stochastic")

    # Parabolic SAR (weight 1)
    psar_signal = calculate_parabolic_sar(high, low, close)
    signals.append(psar_signal)
    weights.append(1)
    indicator_names.append("ParabolicSAR")

    # CCI (weight 1)
    cci_signal = calculate_cci(high, low, close)
    signals.append(cci_signal)
    weights.append(1)
    indicator_names.append("CCI")

    # Williams %R (weight 1)
    willr_signal = calculate_williams_r(high, low, close)
    signals.append(willr_signal)
    weights.append(1)
    indicator_names.append("WilliamsR")

    # MFI (weight 1)
    mfi_signal = calculate_mfi(high, low, close, volume)
    signals.append(mfi_signal)
    weights.append(1)
    indicator_names.append("MFI")

    # OBV (weight 1)
    obv_signal = calculate_obv(close, volume)
    signals.append(obv_signal)
    weights.append(1)
    indicator_names.append("OBV")

    # ROC (weight 1)
    roc_signal = calculate_roc(close)
    signals.append(roc_signal)
    weights.append(1)
    indicator_names.append("ROC")

    # Detect ranging market
    if is_ranging_market(close, adx_val, bb_upper, bb_lower):
        print("Market is ranging. No trade signal.")
        return None

    # Weighted voting
    buy_score = sum(w for s, w in zip(signals, weights) if s == "BUY")
    sell_score = sum(w for s, w in zip(signals, weights) if s == "SELL")

    # Store signals for accuracy tracking
    get_consensus_signal.last_signals = dict(zip(indicator_names, signals))
    # Only take trades when current price crosses previous 1-minute candle's high or low
    df_1min = get_1min_ohlc(symbol, 2)
    if df_1min is not None and len(df_1min) >= 2:
        prev_candle = df_1min.iloc[-2]
        print("Previous candle:", prev_candle)
        prev_high = prev_candle['high']
        prev_low = prev_candle['low']
        current_price = get_current_price()
        print("Current price:", current_price, "Previous high:", prev_high, "Previous low:", prev_low)
        crossed_high = current_price > prev_high
        crossed_low = current_price < prev_low
    else:
        crossed_high = crossed_low = False

    if crossed_high:
        return "BUY"
    elif crossed_low:
        return "SELL"
    else:
        return None

def modify_position_sl_tp(position, new_sl, new_tp=None):
    """Modify the SL (and optionally TP) of an open position."""
    request = {
        "action": mt5.TRADE_ACTION_SLTP,
        "position": position.ticket,
        "symbol": position.symbol,
        "sl": new_sl,
        "tp": new_tp if new_tp is not None else 0.0,  # set to 0.0 if not updating TP
        "magic": 234000,
        "comment": "Update SL/TP to new candle",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }
    result = mt5.order_send(request)
    print(f"Modify SL/TP for position {position.ticket}: {result}")
    return result

def next_round_number_above(level):
    # Snap to next multiple of 10 (e.g., 3290 -> 3300, 3300 -> 3310)
    return ((int(level) // 10) + 1) * 10

def next_round_number_below(level):
    # Snap to previous multiple of 10 (e.g., 3300 -> 3290, 3310 -> 3300)
    return ((int(level) // 10) - 1) * 10

def trading_loop():
    lot_size = 0.01
    entry_ticket = None
    entry_level = None
    entry_side = None
    last_minute_checked = None
    # Track cross counts for each level and direction
    cross_counts = {}

    while True:
        now = datetime.datetime.utcnow()
        current_minute = now.replace(second=0, microsecond=0)
        if last_minute_checked is None or current_minute > last_minute_checked:
            df = get_1min_ohlc(symbol, 2)
            if df is not None and len(df) >= 2:
                prev = df.iloc[-2]
                open_ = int(round(prev['open']))
                high = prev['high']
                low = prev['low']
                close = prev['close']
                # Level selection: all multiples of 10 within ±100 of open, e.g., 3290, 3300, ..., 3470
                start = ((open_ - 100) // 10) * 10
                end = ((open_ + 100) // 10) * 10
                levels = [lvl for lvl in range(start, end + 1, 10)]
                

                # Entry logic: only if not in a position
                if entry_ticket is None:
                    for lvl in levels:
                        tick = mt5.symbol_info_tick(symbol)
                        if tick is None:
                            continue
                        current_price = tick.bid if tick.bid != 0 else tick.ask
                        # Check if current price and level difference is <= 1.5 pips (0.15 for XAUUSD)
                        if abs(current_price - lvl) > 1.5:
                            
                            continue  # Skip this level if difference is more than 1.5 pips
                        # Track cross counts for each level and direction
                        # Long: either previous open < lvl or current open < lvl, and close > lvl
                        crossed_long = (prev['open'] < lvl or df.iloc[-1]['open'] < lvl) and close > lvl
                        crossed_short = (prev['open'] > lvl or df.iloc[-1]['open'] > lvl) and close < lvl
                        if crossed_long or crossed_short:
                            key = (lvl, 'long' if crossed_long else 'short')
                            if key not in cross_counts:
                                cross_counts[key] = 1
                            else:
                                cross_counts[key] += 1
                        # Only take trade on the second cross in the same direction
                        if crossed_long and cross_counts.get((lvl, 'long'), 0) == 2:
                            tp = next_round_number_above(lvl)
                            price = close
                            tick = mt5.symbol_info_tick(symbol)
                            if tick is not None:
                                price = float(price)
                                req = {
                                    "action": mt5.TRADE_ACTION_DEAL,
                                    "symbol": symbol,
                                    "volume": lot_size,
                                    "type": mt5.ORDER_TYPE_BUY,
                                    "price": price,
                                    "tp": float(tp),
                                    "deviation": 10,
                                    "magic": 234000,
                                    "comment": f"RoundLevelLong {lvl}",
                                    "type_time": mt5.ORDER_TIME_GTC,
                                    "type_filling": mt5.ORDER_FILLING_IOC,
                                }
                                res = mt5.order_send(req)
                                print("Open Long:", res)
                                if res is not None and hasattr(res, "retcode") and res.retcode == mt5.TRADE_RETCODE_DONE:
                                    entry_ticket = res.order if hasattr(res, "order") else res.order
                                    entry_level = lvl
                                    entry_side = "long"
                                    log_trade("OPEN_LONG", lot_size, price, entry_ticket)
                                else:
                                    print("OrderSend failed:", res)
                            break
                        elif crossed_short and cross_counts.get((lvl, 'short'), 0) == 2:
                            tp = next_round_number_below(lvl)
                            price = close
                            tick = mt5.symbol_info_tick(symbol)
                            if tick is not None:
                                price = float(price)
                                req = {
                                    "action": mt5.TRADE_ACTION_DEAL,
                                    "symbol": symbol,
                                    "volume": lot_size,
                                    "type": mt5.ORDER_TYPE_SELL,
                                    "price": price,
                                    "tp": float(tp),
                                    "deviation": 10,
                                    "magic": 234000,
                                    "comment": f"RoundLevelShort {lvl}",
                                    "type_time": mt5.ORDER_TIME_GTC,
                                    "type_filling": mt5.ORDER_FILLING_IOC,
                                }
                                res = mt5.order_send(req)
                                print("Open Short:", res)
                                if res is not None and hasattr(res, "retcode") and res.retcode == mt5.TRADE_RETCODE_DONE:
                                    entry_ticket = res.order if hasattr(res, "order") else res.order
                                    entry_level = lvl
                                    entry_side = "short"
                                    log_trade("OPEN_SHORT", lot_size, price, entry_ticket)
                                else:
                                    print("OrderSend failed:", res)
                            break
                else:
                    # Exit/flip logic: manage open position
                    positions = mt5.positions_get(symbol=symbol)
                    pos = None
                    if positions:
                        for p in positions:
                            if p.ticket == entry_ticket:
                                pos = p
                                break
                    if pos is None:
                        pnl = get_latest_closed_trade_pnl(entry_ticket)
                        if pnl is not None:
                            trade_pnl_history.append({
                                "signal": entry_side,
                                "pnl": pnl,
                                "lot_size": lot_size
                            })
                            print_pnl_table()
                        entry_ticket = None
                        entry_level = None
                        entry_side = None
                    else:
                        curr_df = get_1min_ohlc(symbol, 1)
                        if curr_df is not None and len(curr_df) >= 1:
                            curr_close = curr_df.iloc[-1]['close']
                            if entry_side == "long" and curr_close < entry_level:
                                close_long(pos.volume, position_id=pos.ticket)
                                pnl = get_latest_closed_trade_pnl(pos.ticket)
                                if pnl is not None:
                                    trade_pnl_history.append({
                                        "signal": "long",
                                        "pnl": pnl,
                                        "lot_size": lot_size
                                    })
                                    print_pnl_table()
                                price = curr_close
                                tp = next_round_number_below(entry_level)
                                tick = mt5.symbol_info_tick(symbol)
                                if tick is not None:
                                    price = float(price)
                                    req = {
                                        "action": mt5.TRADE_ACTION_DEAL,
                                        "symbol": symbol,
                                        "volume": lot_size,
                                        "type": mt5.ORDER_TYPE_SELL,
                                        "price": price,
                                        "tp": float(tp),
                                        "deviation": 10,
                                        "magic": 234000,
                                        "comment": f"FlipShort {entry_level}",
                                        "type_time": mt5.ORDER_TIME_GTC,
                                        "type_filling": mt5.ORDER_FILLING_IOC,
                                    }
                                    res = mt5.order_send(req)
                                    print("Flip to Short:", res)
                                    if res is not None and hasattr(res, "retcode") and res.retcode == mt5.TRADE_RETCODE_DONE:
                                        entry_ticket = res.order if hasattr(res, "order") else res.order
                                        entry_level = entry_level
                                        entry_side = "short"
                                        log_trade("OPEN_SHORT", lot_size, price, entry_ticket)
                                    else:
                                        print("OrderSend failed:", res)
                                else:
                                    entry_ticket = None
                                    entry_level = None
                                    entry_side = None
                            elif entry_side == "short" and curr_close > entry_level:
                                close_short(pos.volume, position_id=pos.ticket)
                                pnl = get_latest_closed_trade_pnl(pos.ticket)
                                if pnl is not None:
                                    trade_pnl_history.append({
                                        "signal": "short",
                                        "pnl": pnl,
                                        "lot_size": lot_size
                                    })
                                    print_pnl_table()
                                price = curr_close
                                tp = next_round_number_above(entry_level)
                                tick = mt5.symbol_info_tick(symbol)
                                if tick is not None:
                                    price = float(price)
                                    req = {
                                        "action": mt5.TRADE_ACTION_DEAL,
                                        "symbol": symbol,
                                        "volume": lot_size,
                                        "type": mt5.ORDER_TYPE_BUY,
                                        "price": price,
                                        "tp": float(tp),
                                        "deviation": 10,
                                        "magic": 234000,
                                        "comment": f"FlipLong {entry_level}",
                                        "type_time": mt5.ORDER_TIME_GTC,
                                        "type_filling": mt5.ORDER_FILLING_IOC,
                                    }
                                    res = mt5.order_send(req)
                                    print("Flip to Long:", res)
                                    if res is not None and hasattr(res, "retcode") and res.retcode == mt5.TRADE_RETCODE_DONE:
                                        entry_ticket = res.order if hasattr(res, "order") else res.order
                                        entry_level = entry_level
                                        entry_side = "long"
                                        log_trade("OPEN_LONG", lot_size, price, entry_ticket)
                                    else:
                                        print("OrderSend failed:", res)
                                else:
                                    entry_ticket = None
                                    entry_level = None
                                    entry_side = None
            last_minute_checked = current_minute
        time.sleep(0.5)

def get_one_sec_data(symbol, seconds_count):
    """Fetch one-second data for the given symbol and seconds count."""
    utc_now = datetime.datetime.utcnow()
    utc_from = utc_now - timedelta(seconds=seconds_count)
    ticks = mt5.copy_ticks_range(symbol, utc_from, utc_now, mt5.COPY_TICKS_ALL)
    if ticks is None or len(ticks) == 0:
        # Fallback: create dummy 1-second bar using the current tick
        info = mt5.symbol_info_tick(symbol)
        if info is None:
            return None
        current_price = info.bid if info.bid != 0 else info.ask
        now = datetime.datetime.utcnow()
        dummy_data = pd.DataFrame({
            'open': [current_price],
            'high': [current_price],
            'low': [current_price],
            'close': [current_price],
            'volume': [0]
        }, index=[now])
        return dummy_data

    data = pd.DataFrame(ticks)
    # Use 'time_msc' for millisecond accuracy if available; otherwise use 'time'
    if 'time_msc' in data.columns:
        data['time'] = pd.to_datetime(data['time_msc'], unit='ms')
    else:
        data['time'] = pd.to_datetime(data['time'], unit='s')
    data.set_index('time', inplace=True)

    # Aggregate ticks into 1-second OHLC bars
    if 'bid' in data.columns:
        price = data['bid']
    elif 'last' in data.columns:
        price = data['last']
    else:
        price = data.index.to_series().astype(float)
    ohlc = price.resample("1S").ohlc()

    if 'volume' in data.columns:
        vol = data['volume'].resample("1S").sum()
        one_sec_bar = ohlc.join(vol)
    else:
        one_sec_bar = ohlc

    return one_sec_bar

def get_cross_signal(symbol):
    """
    Use 1-minute timeframe.
    If current price crosses the lowest low or highest high of the last 3 closed 1-min candles,
    return "BUY" or "SELL". Otherwise, return None.
    """
    df = get_1min_ohlc(symbol, 4)  # Need last 3 closed candles (so 4 to get last 3 fully closed)
    if df is not None and len(df) >= 4:
        last3 = df.iloc[-4:-1]  # Exclude the currently forming candle
        lowest_low = last3['low'].min()
        highest_high = last3['high'].max()
        current_price = get_current_price()
        if current_price > highest_high:
            return "BUY"
        elif current_price < lowest_low:
            return "SELL"
    return None

def close_position(pos):
    """Close a position (long or short) by ticket."""
    if pos.type == mt5.POSITION_TYPE_BUY:
        close_long(pos.volume, position_id=pos.ticket)
    elif pos.type == mt5.POSITION_TYPE_SELL:
        close_short(pos.volume, position_id=pos.ticket)

# Start the trading loop
if __name__ == "__main__":
    if not mt5.initialize():
        print("Failed to initialize MetaTrader5")
    else:
        # Print the last 5 closed 1-min candles for the current timeframe
        last_5_candles = get_1min_ohlc(symbol, 5)
        if last_5_candles is not None:
            print("Last 5 closed 1-min candles:")
            print(last_5_candles)
        else:
            print("Could not fetch last 5 closed 1-min candles.")
        one_sec_data = get_one_sec_data(symbol, 10)
        if one_sec_data is not None:
            print("Fetched one-second data:")
            print(one_sec_data)
        trading_loop()
        mt5.shutdown()
