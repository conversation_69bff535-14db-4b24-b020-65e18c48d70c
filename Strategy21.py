import MetaTrader5 as mt5
import pandas as pd
from datetime import datetime, timedelta, time
from tabulate import tabulate

# Connect to MetaTrader 10
if not mt5.initialize():
    print("initialize() failed")
    quit()

symbol = "Boom 1000 Index"

# Get last 1 day of 1-min data
utc_to = datetime.now()
utc_from = utc_to - timedelta(days=10)
rates = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_M1, utc_from, utc_to)
mt5.shutdown()

if rates is None or len(rates) == 0:
    print("No data retrieved from MT5.")
    quit()

# Prepare DataFrame
df = pd.DataFrame(rates)
df['Datetime'] = pd.to_datetime(df['time'], unit='s')
df = df.set_index('Datetime')

# Detect upward spikes: large bullish candles with significant jump
spike_body_threshold = 2  # Minimum body size (close - open) to consider as spike
spike_body_ratio = 0.7     # Body must be at least 70% of the total range (high-low)

spikes = []
prev_row = None
for idx, row in df.iterrows():
    open_ = row['open']
    close = row['close']
    high = row['high']
    low = row['low']
    body = close - open_
    range_ = high - low

    # Large bullish candle with big body and body is most of the range
    if (
        body >= spike_body_threshold and
        range_ > 0 and
        (body / range_) >= spike_body_ratio
    ):
        # Optionally, check previous candle was not a spike
        if prev_row is not None:
            prev_body = prev_row['close'] - prev_row['open']
            if prev_body >= spike_body_threshold:
                prev_row = row
                continue  # skip if previous was also a spike
        spikes.append({
            'Datetime': idx,
            'Spike_Open': open_,
            'Spike_Close': close,
            'High': high,
            'Low': low,
            'Body': body,
            'Range': range_
        })
    prev_row = row

spikes_df = pd.DataFrame(spikes)
if not spikes_df.empty:
    print("Upward spikes detected (first 30 shown):")
    print(tabulate(spikes_df, headers='keys', tablefmt='fancy_grid', showindex=False))
    spikes_df.to_csv('boom1000_upward_spikes.csv', index=False)

    # --- Total number of spikes and total range covered by spikes per day ---
    spikes_df['Date'] = spikes_df['Datetime'].dt.date
    # Calculate range for each spike (high - open)
    spikes_df['Spike_Range'] = spikes_df['High'] - spikes_df['Spike_Open']
    spikes_per_day = (
        spikes_df.groupby('Date')
        .agg(Total_Spikes=('Datetime', 'count'), Total_Range=('Spike_Range', 'sum'))
        .reset_index()
    )
    print("\nTotal number of spikes and total range (points) covered by spikes per day:")
    print(tabulate(spikes_per_day, headers='keys', tablefmt='fancy_grid', showindex=False))

    # --- 4-hour interval analysis ---
    spikes_df['Hour'] = spikes_df['Datetime'].dt.hour
    spikes_df['4H_Interval'] = spikes_df['Hour'] // 4 * 4
    spikes_df['Interval_Label'] = spikes_df['4H_Interval'].apply(lambda h: f"{h:02d}:00-{(h+4)%24:02d}:00")

    # Count spikes per date and interval
    interval_counts = (
        spikes_df.groupby(['Date', 'Interval_Label'])
        .size()
        .reset_index(name='Spike_Count')
    )

    # For each date, find the interval with the maximum spikes
    max_intervals = (
        interval_counts.sort_values(['Date', 'Spike_Count'], ascending=[True, False])
        .groupby('Date')
        .first()
        .reset_index()
    )

    print("\nMost spike-prone 4-hour interval per date:")
    print(tabulate(max_intervals, headers='keys', tablefmt='fancy_grid', showindex=False))

    # --- Find the common interval with maximum spikes across all dates ---
    total_interval_counts = (
        spikes_df.groupby('Interval_Label')
        .size()
        .reset_index(name='Total_Spike_Count')
        .sort_values('Total_Spike_Count', ascending=False)
    )
    max_total = total_interval_counts['Total_Spike_Count'].max()
    most_common_intervals = total_interval_counts[total_interval_counts['Total_Spike_Count'] == max_total]

    print("\n4-hour interval(s) with maximum total spikes across all dates:")
    print(tabulate(most_common_intervals, headers='keys', tablefmt='fancy_grid', showindex=False))

    # --- Extract spikes where close price ends with 11 or 08 ---
    def last_two_digits(val):
        # Convert to string, remove decimals, get last two digits
        s = str(int(round(val)))
        return s[-2:]

    spikes_df['Close_Last2'] = spikes_df['Spike_Close'].apply(last_two_digits)
    filtered_spikes = spikes_df[spikes_df['Close_Last2'].isin(['11', '08'])]

    # Group by the close price (rounded, as string), count and sum range
    price_stats = (
        filtered_spikes.groupby('Spike_Close')
        .agg(Spike_Count=('Spike_Close', 'count'), Total_Range=('Spike_Range', 'sum'))
        .reset_index()
        .sort_values('Spike_Close')
    )

    if not price_stats.empty:
        print("\nSpikes where close price ends with 11 or 08:")
        print(tabulate(price_stats, headers='keys', tablefmt='fancy_grid', showindex=False))
    else:
        print("\nNo spikes found where close price ends with 11 or 08.")

    # --- Find 1-hour intervals where no spikes occurred in past 10 days ---
    spikes_df['Hour_Label'] = spikes_df['Datetime'].dt.hour.apply(lambda h: f"{h:02d}:00-{(h+1)%24:02d}:00")
    hour_counts = (
        spikes_df.groupby('Hour_Label')
        .size()
        .reset_index(name='Total_Spike_Count')
    )
    all_hour_labels = [f"{h:02d}:00-{(h+1)%24:02d}:00" for h in range(24)]
    hours_with_spikes = set(hour_counts['Hour_Label'])
    hours_without_spikes = [hl for hl in all_hour_labels if hl not in hours_with_spikes]

    print("\n1-hour interval(s) with ZERO spikes in the past 10 days:")
    if hours_without_spikes:
        print(tabulate([[hl] for hl in hours_without_spikes], headers=['Interval'], tablefmt='fancy_grid', showindex=False))
    else:
        print("All 1-hour intervals had at least one spike.")

    # --- Find the longest continuous interval with no spikes (x hours/minutes) ---
    # Create a boolean series for all minutes in the data range
    all_minutes = pd.date_range(start=df.index.min(), end=df.index.max(), freq='T')
    spike_minutes = set(spikes_df['Datetime'])
    no_spike_mask = ~all_minutes.isin(spike_minutes)

    # Find the longest continuous stretch of True (no spike)
    max_len = 0
    cur_len = 0
    start_idx = None
    max_start = None
    max_end = None

    for i, has_no_spike in enumerate(no_spike_mask):
        if has_no_spike:
            if cur_len == 0:
                start_idx = i
            cur_len += 1
            if cur_len > max_len:
                max_len = cur_len
                max_start = start_idx
                max_end = i
        else:
            cur_len = 0

    if max_len > 0:
        interval_start = all_minutes[max_start]
        interval_end = all_minutes[max_end]
        duration = interval_end - interval_start
        print(f"\nLongest continuous interval with NO spikes: {interval_start} to {interval_end} "
              f"({duration} duration, {max_len} minutes)")
    else:
        print("\nNo interval without spikes found (spikes in every minute).")

    # --- Find the longest continuous interval with no spikes (x hours/minutes) common to all 10 days ---
    # Prepare a set of all days in the data
    all_dates = pd.date_range(df.index.min().date(), df.index.max().date(), freq='D')
    # For each day, get set of minutes with no spike
    minutes_per_day = {}
    for date in all_dates:
        day_minutes = pd.date_range(start=pd.Timestamp(date) + pd.Timedelta(minutes=0),
                                   end=pd.Timestamp(date) + pd.Timedelta(hours=23, minutes=59),
                                   freq='T')
        day_spike_minutes = set(spikes_df[spikes_df['Datetime'].dt.date == date.date()]['Datetime'].dt.time)
        minutes_per_day[date.date()] = set(day_minutes.time) - day_spike_minutes

    # Find intersection: minutes with no spike on ALL days
    if minutes_per_day:
        common_no_spike_minutes = set.intersection(*minutes_per_day.values())
    else:
        common_no_spike_minutes = set()

    if common_no_spike_minutes:
        # Sort times for easier interval finding
        sorted_times = sorted(common_no_spike_minutes)
        # Find longest continuous interval
        max_len = 0
        cur_len = 1
        max_start = sorted_times[0]
        cur_start = sorted_times[0]
        prev_time = sorted_times[0]
        for t in sorted_times[1:]:
            # Check if this minute is consecutive to previous
            prev_dt = pd.Timestamp.combine(pd.Timestamp('2000-01-01'), prev_time)
            cur_dt = pd.Timestamp.combine(pd.Timestamp('2000-01-01'), t)
            if (cur_dt - prev_dt).seconds == 60:
                cur_len += 1
            else:
                if cur_len > max_len:
                    max_len = cur_len
                    max_start = cur_start
                    max_end = prev_time
                cur_len = 1
                cur_start = t
            prev_time = t
        # Check last interval
        if cur_len > max_len:
            max_len = cur_len
            max_start = cur_start
            max_end = prev_time

        print(f"\nLongest continuous interval with NO spikes (common to all 10 days): "
              f"{max_start.strftime('%H:%M')} to {max_end.strftime('%H:%M')} "
              f"({max_len} minutes)")
    else:
        print("\nNo common interval without spikes found for all 10 days.")
else:
    print("No upward spikes detected in the last 1 day.")