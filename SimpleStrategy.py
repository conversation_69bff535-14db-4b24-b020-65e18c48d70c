import MetaTrader5 as mt5
import time

def open_hedge_trades(symbol, lot):
    tick = mt5.symbol_info_tick(symbol)
    if tick is None:
        print("Failed to get tick for", symbol)
        return None
    # Ensure the symbol is selected
    if not mt5.symbol_select(symbol, True):
        print("Failed to select symbol", symbol)
        return None

    # Create Buy order request
    request_buy = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": lot,
        "type": mt5.ORDER_TYPE_BUY,
        "price": tick.ask,
        "deviation": 10,
        "magic": 234000,
        "comment": "Hedged Scalping Buy",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }
    result_buy = mt5.order_send(request_buy)
    
    # Create Sell order request
    request_sell = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": lot,
        "type": mt5.ORDER_TYPE_SELL,
        "price": tick.bid,
        "deviation": 10,
        "magic": 234000,
        "comment": "Hedged Scalping Sell",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }
    result_sell = mt5.order_send(request_sell)
    
    if result_buy.retcode != mt5.TRADE_RETCODE_DONE or result_sell.retcode != mt5.TRADE_RETCODE_DONE:
        print("Order send failed:", result_buy, result_sell)
        return None
    return {
        'buy': {"ticket": result_buy.order, "price": tick.ask, "type": "buy", "symbol": symbol},
        'sell': {"ticket": result_sell.order, "price": tick.bid, "type": "sell", "symbol": symbol}
    }

def close_trade(order_info):
    ticket = order_info["ticket"]
    symbol = order_info["symbol"]
    positions = mt5.positions_get(symbol=symbol)
    pos_to_close = None
    for pos in positions:
        if pos.ticket == ticket:
            pos_to_close = pos
            break
    if not pos_to_close:
        print("Position not found for ticket", ticket)
        return
    tick = mt5.symbol_info_tick(symbol)
    if order_info["type"] == "buy":
        price = tick.bid
        order_type = mt5.ORDER_TYPE_SELL
    else:
        price = tick.ask
        order_type = mt5.ORDER_TYPE_BUY
    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": pos_to_close.volume,
        "type": order_type,
        "position": ticket,
        "price": price,
        "deviation": 10,
        "magic": 234000,
        "comment": "Close position",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }
    result = mt5.order_send(request)
    if result.retcode != mt5.TRADE_RETCODE_DONE:
        print("Failed to close position", ticket, result)

def get_trade_profit(ticket):
    # Check open positions first.
    positions = mt5.positions_get()
    if positions:
        for pos in positions:
            if pos.ticket == ticket:
                return pos.profit
    # If position not open, check deal history (assuming trade completed recently).
    now = time.time()
    history = mt5.history_deals_get(now - 86400, now)
    if history:
        for deal in history:
            if deal.ticket == ticket:
                return deal.profit
    return 0.0

def check_for_reversal_signal(active_order, symbol, threshold=0.0005):
    tick = mt5.symbol_info_tick(symbol)
    if not tick:
        return False
    # For a BUY trade, if current bid has fallen significantly below the entry price, consider it a reversal.
    if active_order["type"] == "buy":
        if tick.bid < active_order["price"] - threshold:
            return True
    # For a SELL trade, if current ask has risen significantly above the entry price, it's a reversal.
    else:
        if tick.ask > active_order["price"] + threshold:
            return True
    return False

def get_session_net_profit(trade_ids):
    net_profit = 0
    for order_info in trade_ids:
        net_profit += get_trade_profit(order_info["ticket"])
    return net_profit

def open_reverse_trade(active_order, symbol, lot):
    # Opens a trade in the reverse direction of the active order.
    tick = mt5.symbol_info_tick(symbol)
    if not tick:
        print("Failed to get tick for reverse trade", symbol)
        return None
    order_type = mt5.ORDER_TYPE_SELL if active_order["type"] == "buy" else mt5.ORDER_TYPE_BUY
    price = tick.ask if order_type == mt5.ORDER_TYPE_BUY else tick.bid
    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": lot,
        "type": order_type,
        "price": price,
        "deviation": 10,
        "magic": 234000,
        "comment": "Hedged Scalping Reverse Trade",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }
    result = mt5.order_send(request)
    if result.retcode != mt5.TRADE_RETCODE_DONE:
        print("Failed to open reverse trade for", symbol, result)
        return None
    return {"ticket": result.order, "price": price, "type": "buy" if order_type == mt5.ORDER_TYPE_BUY else "sell", "symbol": symbol}

def hedged_scalping_strategy(symbol="XAUUSD", lot=0.01):
    while True:
        # Entry Condition – No Open Positions:
        positions = mt5.positions_get(symbol=symbol)
        if positions is None or len(positions) == 0:
            print("No open positions detected. Executing hedge trades.")
            trade_info = open_hedge_trades(symbol, lot)
            if trade_info is None:
                time.sleep(1)
                continue

            time.sleep(5)  # Monitoring phase wait period

            # Evaluate trades for immediate loss management:
            buy_profit = get_trade_profit(trade_info['buy']["ticket"])
            sell_profit = get_trade_profit(trade_info['sell']["ticket"])
            if buy_profit < sell_profit:
                print("Buy trade is losing. Closing it.")
                close_trade(trade_info['buy'])
                active_order = trade_info['sell']
            else:
                print("Sell trade is losing. Closing it.")
                close_trade(trade_info['sell'])
                active_order = trade_info['buy']

            active_trades = [active_order]
            reversal_taken = False
            max_profit = get_trade_profit(active_order["ticket"])
            drop_threshold = 0.1  # set your desired drop threshold

            # Monitor active trades for reversal, profit drop, and profit booking:
            while True:
                time.sleep(1)
                # NEW: If both hedged trades are negative, wait until one becomes profitable
                if len(active_trades) == 2:
                    profit1 = get_trade_profit(active_trades[0]["ticket"])
                    profit2 = get_trade_profit(active_trades[1]["ticket"])
                    if profit1 < 0 and profit2 < 0:
                        print("Both hedged trades are negative; waiting for one to turn profitable.")
                        continue
                    elif profit1 >= 0 and profit2 < 0:
                        print("Trade", active_trades[0]["ticket"], "has turned profitable; closing losing trade", active_trades[1]["ticket"])
                        close_trade(active_trades[1])
                        active_trades = [active_trades[0]]
                        active_order = active_trades[0]
                        reversal_taken = False
                        max_profit = get_trade_profit(active_order["ticket"])
                    elif profit2 >= 0 and profit1 < 0:
                        print("Trade", active_trades[1]["ticket"], "has turned profitable; closing losing trade", active_trades[0]["ticket"])
                        close_trade(active_trades[0])
                        active_trades = [active_trades[1]]
                        active_order = active_trades[0]
                        reversal_taken = False
                        max_profit = get_trade_profit(active_order["ticket"])

                # Check if the trade shows a reversal signal.
                if not reversal_taken and check_for_reversal_signal(active_order, symbol):
                    print("Reversal detected for ticket", active_order["ticket"])
                    reverse_order = open_reverse_trade(active_order, symbol, lot)
                    if reverse_order:
                        active_trades.append(reverse_order)
                        reversal_taken = True
                        print("Opened reverse trade for hedging. Active trades:",
                              [trade["ticket"] for trade in active_trades])
                        time.sleep(5)  # wait before deciding winning direction
                        profit1 = get_trade_profit(active_trades[0]["ticket"])
                        profit2 = get_trade_profit(active_trades[1]["ticket"])
                        if profit1 < profit2:
                            print("After 5 sec: Trade", active_trades[0]["ticket"], "is losing. Closing it.")
                            close_trade(active_trades[0])
                            active_trades = [active_trades[1]]
                        else:
                            print("After 5 sec: Trade", active_trades[1]["ticket"], "is losing. Closing it.")
                            close_trade(active_trades[1])
                            active_trades = [active_trades[0]]
                        active_order = active_trades[0]
                        reversal_taken = False
                        max_profit = get_trade_profit(active_order["ticket"])
                
                # Update maximum profit and check for a drop.
                current_profit = get_trade_profit(active_order["ticket"])
                if current_profit > max_profit:
                    max_profit = current_profit
                elif (max_profit - current_profit) >= drop_threshold and not reversal_taken:
                    print("Profit drop detected for ticket", active_order["ticket"])
                    reverse_order = open_reverse_trade(active_order, symbol, lot)
                    if reverse_order:
                        active_trades.append(reverse_order)
                        reversal_taken = True
                        print("Opened hedge on profit drop. Active trades:",
                              [trade["ticket"] for trade in active_trades])
                        time.sleep(5)  # wait 5 sec to decide winning direction
                        profit1 = get_trade_profit(active_trades[0]["ticket"])
                        profit2 = get_trade_profit(active_trades[1]["ticket"])
                        if profit1 < profit2:
                            print("After profit drop hedge: Closing losing trade", active_trades[0]["ticket"])
                            close_trade(active_trades[0])
                            active_trades = [active_trades[1]]
                        else:
                            print("After profit drop hedge: Closing losing trade", active_trades[1]["ticket"])
                            close_trade(active_trades[1])
                            active_trades = [active_trades[0]]
                        active_order = active_trades[0]
                        reversal_taken = False
                        max_profit = get_trade_profit(active_order["ticket"])
                
                combined_profit = get_session_net_profit(active_trades)
                if combined_profit >= 1.5:
                    print("Profit target reached. Closing trades:",
                          [trade["ticket"] for trade in active_trades])
                    for order in active_trades:
                        close_trade(order)
                    break
        time.sleep(1)

if __name__ == "__main__":
    if not mt5.initialize():
        print("initialize() failed")
        mt5.shutdown()
    try:
        hedged_scalping_strategy()
    finally:
        mt5.shutdown()
