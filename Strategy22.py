import MetaTrader5 as mt5
import pandas as pd
import time
from datetime import datetime, timedelta
from tabulate import tabulate

symbol = "Boom 300 Index"
lot_size = 0.2  # Adjust as needed

trade_history = []

def get_1min_ohlc(symbol, bars=10):
    rates = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_M5, 0, bars)
    if rates is None or len(rates) < bars:
        return None
    df = pd.DataFrame(rates)
    df['Datetime'] = pd.to_datetime(df['time'], unit='s')
    df.set_index('Datetime', inplace=True)
    return df

def find_last_bullish_candle(df):
    # Find the last bullish candle with body > 4 in the DataFrame (excluding current forming candle)
    for idx in reversed(df.index):
        row = df.loc[idx]
        body = row['close'] - row['open']
        if row['close'] > row['open']:
            return {'Datetime': idx, 'Spike_Open': row['open'], 'Spike_Close': row['close']}
    return None

def get_current_price():
    tick = mt5.symbol_info_tick(symbol)
    return tick.bid if tick else None

def open_long(lot, price):
    req = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": lot,
        "type": mt5.ORDER_TYPE_BUY,
        "price": price,
        "deviation": 10,
        "magic": 234001,
        "comment": "SpikeLong",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }
    res = mt5.order_send(req)
    if res and res.retcode == mt5.TRADE_RETCODE_DONE:
        return res.order
    return None

def close_position(ticket):
    positions = mt5.positions_get(ticket=ticket)
    if not positions:
        return False
    pos = positions[0]
    price = get_current_price()
    req = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": pos.volume,
        "type": mt5.ORDER_TYPE_SELL,
        "position": ticket,
        "price": price,
        "deviation": 10,
        "magic": 234001,
        "comment": "SpikeClose",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }
    res = mt5.order_send(req)
    return res and res.retcode == mt5.TRADE_RETCODE_DONE

def get_closed_pnl(ticket):
    deals = mt5.history_deals_get(position=ticket)
    if deals:
        return sum(d.profit for d in deals)
    return 0

def print_trade_table():
    if not trade_history:
        print("No trades yet.")
        return
    headers = ["#", "Entry Time", "Entry Price", "Exit Time", "Exit Price", "PnL"]
    data = []
    for i, t in enumerate(trade_history):
        data.append([
            i+1, t['entry_time'], t['entry_price'],
            t['exit_time'], t['exit_price'], round(t['pnl'], 2)
        ])
    print(tabulate(data, headers=headers, tablefmt="fancy_grid"))

def backtest_strategy():
    print("Running backtest for last 5 days...")
    # Fetch last 5 days of 1-min data
    utc_to = datetime.now()
    utc_from = utc_to - timedelta(days=90)
    if not mt5.initialize():
        print("MT5 initialization failed")
        return
    rates = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_M5, utc_from, utc_to)
    mt5.shutdown()
    if rates is None or len(rates) == 0:
        print("No data retrieved from MT5.")
        return
    df = pd.DataFrame(rates)
    df['Datetime'] = pd.to_datetime(df['time'], unit='s')
    df.set_index('Datetime', inplace=True)
    open_trade = None
    trade_history_bt = []
    for i in range(2, len(df)):
        # Use all candles up to previous one to find last bullish
        df_slice = df.iloc[:i]
        last_spike = find_last_bullish_candle(df_slice.iloc[:-1])
        current_row = df.iloc[i]
        prev_row = df.iloc[i-1]
        now = current_row.name
        # Skip trading activity for 16:00-20:00 interval
        hour = now.hour
        if 16 <= hour < 20:
            continue
        # Entry: cross below and close below last bullish candle open, and previous low did not cross
        if last_spike and not open_trade:
            prev_spike_open = last_spike['Spike_Open']
            prev_spike_close = last_spike['Spike_Close']
            prev_spike_time = last_spike['Datetime']
            ads_pips = 0.5 * abs(prev_spike_open - prev_spike_close)
            if ads_pips == 0:
                continue  # Avoid division by zero or zero-hold
            if now - prev_spike_time > timedelta(seconds=20):
                crossed = (current_row['low'] < prev_spike_open)
                closed_below = current_row['close'] < prev_spike_open
                if crossed and closed_below:
                    entry_price = prev_spike_open
                    entry_time = now
                    open_trade = {
                        'entry_time': entry_time,
                        'entry_price': entry_price,
                        'ads_pips': ads_pips,
                        'prev_candle_high': prev_row['high'],
                        'entry_idx': i
                    }
        # Exit condition: close if high above previous candle high or price moves by ads_pips in your favor
        if open_trade:
            exit_trade = False
            exit_reason = ""
            # If current high > previous candle high at entry, close trade
            if current_row['high'] > open_trade['prev_candle_high']:
                exit_trade = True
                exit_reason = "High crossed above previous candle high"
            else:
                # Check if price moved in favor by ads_pips (SHORT: entry - current close >= ads_pips)
                price_move = open_trade['entry_price'] - current_row['close']
                if price_move >= open_trade['ads_pips']:
                    exit_trade = True
                    exit_reason = f"ADS pip target reached ({open_trade['ads_pips']:.2f})"
            if exit_trade:
                exit_price = current_row['close']
                pnl = (open_trade['entry_price'] - exit_price) * lot_size  # SHORT: entry - exit
                taxed_pnl = pnl - 0.33  # Apply tax
                trade_history_bt.append({
                    'entry_time': open_trade['entry_time'].strftime("%Y-%m-%d %H:%M:%S"),
                    'entry_price': open_trade['entry_price'],
                    'exit_time': now.strftime("%Y-%m-%d %H:%M:%S"),
                    'exit_price': exit_price,
                    'pnl': pnl,
                    'taxed_pnl': taxed_pnl,
                    'exit_reason': exit_reason
                })
                open_trade = None
    # Print summary
    if not trade_history_bt:
        print("No trades triggered in backtest.")
    else:
        headers = ["#", "Entry Time", "Entry Price", "Exit Time", "Exit Price", "PnL", "Taxed PnL", "Exit Reason"]
        data = []
        for i, t in enumerate(trade_history_bt):
            data.append([
                i+1, t['entry_time'], t['entry_price'],
                t['exit_time'], t['exit_price'], round(t['pnl'], 2), round(t['taxed_pnl'], 2), t.get('exit_reason', '')
            ])
        print(tabulate(data, headers=headers, tablefmt="fancy_grid"))
        total_pnl = sum(t['pnl'] for t in trade_history_bt)
        total_taxed_pnl = sum(t['taxed_pnl'] for t in trade_history_bt)
        print(f"\nTotal Trades: {len(trade_history_bt)} | Total PnL: {total_pnl:.2f} | Net PnL after tax: {total_taxed_pnl:.2f}")

        # Print table of negative PnL trades
        negative_trades = [t for t in trade_history_bt if t['pnl'] < 0]
        if negative_trades:
            print("\nTrades with Negative PnL:")
            neg_data = []
            for i, t in enumerate(negative_trades):
                neg_data.append([
                    i+1, t['entry_time'], t['entry_price'],
                    t['exit_time'], t['exit_price'], round(t['pnl'], 2), round(t['taxed_pnl'], 2), t.get('exit_reason', '')
                ])
            print(tabulate(neg_data, headers=headers, tablefmt="fancy_grid"))
        else:
            print("\nNo trades with negative PnL.")

        # Print net PnL for each day
        print("\nNet PnL for each day:")
        for t in trade_history_bt:
            t['exit_date'] = t['exit_time'][:10]
        daily_pnl = {}
        daily_taxed_pnl = {}
        for t in trade_history_bt:
            date = t['exit_date']
            daily_pnl[date] = daily_pnl.get(date, 0) + t['pnl']
            daily_taxed_pnl[date] = daily_taxed_pnl.get(date, 0) + t['taxed_pnl']
        daily_data = []
        for date in sorted(daily_pnl.keys()):
            daily_data.append([date, round(daily_pnl[date], 2), round(daily_taxed_pnl[date], 2)])
        print(tabulate(daily_data, headers=["Date", "Net PnL", "Net Taxed PnL"], tablefmt="fancy_grid"))

        # Save daily_data to CSV
        import csv
        with open("daily_pnl.csv", "w", newline="") as f:
            writer = csv.writer(f)
            writer.writerow(["Date", "Net PnL", "Net Taxed PnL"])
            writer.writerows(daily_data)

        # --- 4-hour interval analysis for negative PnL ---
        # Add 4-hour interval label to each negative trade
        for t in negative_trades:
            exit_dt = datetime.strptime(t['exit_time'], "%Y-%m-%d %H:%M:%S")
            interval_start = (exit_dt.hour // 4) * 4
            interval_label = f"{interval_start:02d}:00-{(interval_start+4)%24:02d}:00"
            t['4h_interval'] = interval_label
            t['exit_date'] = exit_dt.date()

        # Table: total negative pnl per 4-hour interval (all days combined)
        neg_pnl_by_interval = {}
        neg_taxed_pnl_by_interval = {}
        for t in negative_trades:
            key = t['4h_interval']
            neg_pnl_by_interval[key] = neg_pnl_by_interval.get(key, 0) + t['pnl']
            neg_taxed_pnl_by_interval[key] = neg_taxed_pnl_by_interval.get(key, 0) + t['taxed_pnl']
        neg_pnl_table = []
        for interval in sorted(neg_pnl_by_interval.keys()):
            neg_pnl_table.append([interval, round(neg_pnl_by_interval[interval], 2), round(neg_taxed_pnl_by_interval[interval], 2)])
        print("\nTotal Negative PnL by 4-hour Interval (all days):")
        print(tabulate(neg_pnl_table, headers=["4H Interval", "Total Negative PnL", "Total Negative Taxed PnL"], tablefmt="fancy_grid"))

        # Build day_interval_neg before using it
        day_interval_neg = {}
        for t in negative_trades:
            day = t['exit_date']
            interval = t['4h_interval']
            day_interval_neg.setdefault(day, {})
            day_interval_neg[day][interval] = day_interval_neg[day].get(interval, 0) + t['pnl']

        # For each day, find the interval with max negative pnl
        max_neg_intervals_per_day = {}
        for day, interval_dict in day_interval_neg.items():
            if interval_dict:
                min_interval = min(interval_dict.items(), key=lambda x: x[1])  # Most negative
                max_neg_intervals_per_day[day] = min_interval[0]

        # Find the 4h interval(s) common to all days as max negative
        from collections import Counter
        interval_counter = Counter(max_neg_intervals_per_day.values())
        common_intervals = [interval for interval, count in interval_counter.items() if count == len(max_neg_intervals_per_day)]

        if common_intervals:
            print("\n4-hour interval(s) with maximum negative PnL common to all days:")
            print(tabulate([[iv] for iv in common_intervals], headers=["4H Interval"], tablefmt="fancy_grid"))
        else:
            print("\nNo single 4-hour interval with maximum negative PnL common to all days.")

        # Table: total negative pnl (sum of all negative trades)
        total_negative_pnl = sum(t['pnl'] for t in negative_trades)
        total_negative_taxed_pnl = sum(t['taxed_pnl'] for t in negative_trades)
        print(f"\nTotal Negative PnL (all trades): {round(total_negative_pnl, 2)} | Total Negative Taxed PnL: {round(total_negative_taxed_pnl, 2)}")
        # --- Exclude trades in common negative 4h interval(s) and recalculate net PnL ---
        if common_intervals:
            # Exclude trades whose exit_time falls in any of the common_intervals
            filtered_trades = [t for t in trade_history_bt if not (
                'exit_time' in t and
                f"{(datetime.strptime(t['exit_time'], '%Y-%m-%d %H:%M:%S').hour // 4) * 4:02d}:00-{((datetime.strptime(t['exit_time'], '%Y-%m-%d %H:%M:%S').hour // 4) * 4 + 4)%24:02d}:00"
                in common_intervals
            )]
            filtered_total_pnl = sum(t['pnl'] for t in filtered_trades)
            filtered_total_taxed_pnl = sum(t['taxed_pnl'] for t in filtered_trades)
            print(f"\nNet PnL after excluding trades in common negative 4H interval(s): {round(filtered_total_pnl, 2)} | Net Taxed PnL: {round(filtered_total_taxed_pnl, 2)}")

            # Net PnL for each day after exclusion
            print("\nNet PnL for each day (excluding common negative 4H interval):")
            daily_pnl_excl = {}
            daily_taxed_pnl_excl = {}
            for t in filtered_trades:
                date = t['exit_time'][:10]
                daily_pnl_excl[date] = daily_pnl_excl.get(date, 0) + t['pnl']
                daily_taxed_pnl_excl[date] = daily_taxed_pnl_excl.get(date, 0) + t['taxed_pnl']
            daily_data_excl = []
            for date in sorted(daily_pnl_excl.keys()):
                daily_data_excl.append([date, round(daily_pnl_excl[date], 2), round(daily_taxed_pnl_excl[date], 2)])
            print(tabulate(daily_data_excl, headers=["Date", "Net PnL (Excl. Neg 4H)", "Net Taxed PnL (Excl. Neg 4H)"], tablefmt="fancy_grid"))
        else:
            print("\nNo common 4-hour interval to exclude for recalculation.")

        # Exclude trades in all 4-hour intervals (exclude all intervals in the table above)
        exclude_intervals = [
            "00:00-04:00",
            "04:00-08:00",
            "08:00-12:00",
            "12:00-16:00",
            "16:00-20:00",
            "20:00-00:00"
        ]
        filtered_trades_all_excluded = [t for t in trade_history_bt if not (
            'exit_time' in t and
            f"{(datetime.strptime(t['exit_time'], '%Y-%m-%d %H:%M:%S').hour // 4) * 4:02d}:00-{((datetime.strptime(t['exit_time'], '%Y-%m-%d %H:%M:%S').hour // 4) * 4 + 4)%24:02d}:00"
            in exclude_intervals
        )]
        filtered_total_pnl_all_excluded = sum(t['pnl'] for t in filtered_trades_all_excluded)
        filtered_total_taxed_pnl_all_excluded = sum(t['taxed_pnl'] for t in filtered_trades_all_excluded)
        print(f"\nNet PnL after excluding ALL 4H intervals listed above: {round(filtered_total_pnl_all_excluded, 2)} | Net Taxed PnL: {round(filtered_total_taxed_pnl_all_excluded, 2)}")

        # Net PnL for each day after exclusion
        print("\nNet PnL for each day (excluding ALL 4H intervals listed above):")
        daily_pnl_excl_all = {}
        daily_taxed_pnl_excl_all = {}
        for t in filtered_trades_all_excluded:
            date = t['exit_time'][:10]
            daily_pnl_excl_all[date] = daily_pnl_excl_all.get(date, 0) + t['pnl']
            daily_taxed_pnl_excl_all[date] = daily_taxed_pnl_excl_all.get(date, 0) + t['taxed_pnl']
        daily_data_excl_all = []
        for date in sorted(daily_pnl_excl_all.keys()):
            daily_data_excl_all.append([date, round(daily_pnl_excl_all[date], 2), round(daily_taxed_pnl_excl_all[date], 2)])
        print(tabulate(daily_data_excl_all, headers=["Date", "Net PnL (Excl. All 4H)", "Net Taxed PnL (Excl. All 4H)"], tablefmt="fancy_grid"))

if __name__ == "__main__":
    backtest_strategy()