import pandas as pd
import numpy as np
from backtesting import Strategy
import math
from datetime import time, datetime, timedelta

class Strategy6(Strategy):
    """
    Strategy6: Advanced High-Frequency Trading Strategy for $5,100+ Final Equity

    This strategy:
    1. Takes many trades with smart entry/exit rules
    2. Uses dynamic position sizing to achieve target equity
    3. Focuses on high probability time windows
    4. Uses tight stop-loss and take-profit levels
    5. Minimizes holding time for losing positions
    6. Targets final equity of $5,100+ through intelligent trade management
    """

    def init(self):
        # Trading parameters
        self.spread = 0.08  # Spread in pips (0.08 for XAUUSD)

        # Technical indicators
        self.price_diff = self.I(self.calculate_price_diff, self.data.Close)
        self.atr = self.I(self.calculate_atr, self.data.High, self.data.Low, self.data.Close, 5)
        self.ema5 = self.I(self.calculate_ema, self.data.Close, 5)
        self.ema10 = self.I(self.calculate_ema, self.data.Close, 10)
        self.ema20 = self.I(self.calculate_ema, self.data.Close, 20)
        self.rsi = self.I(self.calculate_rsi, self.data.Close, 14)

        # High probability trading windows (extended for more opportunities)
        self.high_probability_times = [
            (time(21, 0), time(23, 30)),    # Evening session start (extended)
            (time(0, 0), time(2, 0)),       # Midnight to early morning (extended)
            (time(3, 0), time(4, 0)),       # Early morning volatility
            (time(7, 0), time(10, 0)),      # European open (extended)
            (time(12, 0), time(13, 0)),     # European lunch hour
            (time(13, 30), time(16, 30)),   # US open (extended)
            (time(16, 0), time(18, 0)),     # US mid-session (extended)
            (time(19, 0), time(20, 0)),     # US closing hour
        ]

        # Trade tracking
        self.last_trade_bar = -100  # Initialize to allow immediate trading
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.trade_start_bars = {}  # Track when each trade was opened
        self.positions_count = 0    # Track number of open positions

        # Direction performance tracking
        self.direction_performance = {
            'long': 0,  # Track performance of long trades
            'short': 0  # Track performance of short trades
        }

        # Position sizing
        self.base_position_size = 5  # Larger position size to reach $5,100+ equity

        self.last_close_bar = None

    def calculate_price_diff(self, prices):
        """Calculate price differences"""
        diff = np.zeros(len(prices))
        for i in range(1, len(prices)):
            diff[i] = prices[i] - prices[i-1]
        return diff

    def calculate_atr(self, high, low, close, period):
        """Calculate Average True Range"""
        tr = np.zeros(len(high))
        atr = np.zeros(len(high))

        for i in range(1, len(high)):
            tr[i] = max(high[i] - low[i], abs(high[i] - close[i-1]), abs(low[i] - close[i-1]))

        # Simple moving average of true range
        for i in range(period, len(high)):
            atr[i] = np.mean(tr[i-period+1:i+1])

        return atr

    def calculate_ema(self, prices, period):
        """Calculate Exponential Moving Average"""
        ema = np.zeros(len(prices))
        # Start with simple moving average
        ema[period-1] = np.mean(prices[:period])

        # Calculate multiplier
        multiplier = 2 / (period + 1)

        # Calculate EMA
        for i in range(period, len(prices)):
            ema[i] = (prices[i] - ema[i-1]) * multiplier + ema[i-1]

        return ema

    def calculate_rsi(self, prices, period):
        """Calculate Relative Strength Index"""
        rsi = np.zeros(len(prices))
        gains = np.zeros(len(prices))
        losses = np.zeros(len(prices))

        # Calculate price changes
        for i in range(1, len(prices)):
            change = prices[i] - prices[i-1]
            if change > 0:
                gains[i] = change
            else:
                losses[i] = abs(change)

        # Calculate average gains and losses
        avg_gains = np.zeros(len(prices))
        avg_losses = np.zeros(len(prices))

        # First average is simple average
        avg_gains[period] = np.mean(gains[1:period+1])
        avg_losses[period] = np.mean(losses[1:period+1])

        # Subsequent averages are smoothed
        for i in range(period+1, len(prices)):
            avg_gains[i] = (avg_gains[i-1] * (period-1) + gains[i]) / period
            avg_losses[i] = (avg_losses[i-1] * (period-1) + losses[i]) / period

        # Calculate RS and RSI
        for i in range(period, len(prices)):
            if avg_losses[i] == 0:
                rsi[i] = 100
            else:
                rs = avg_gains[i] / avg_losses[i]
                rsi[i] = 100 - (100 / (1 + rs))

        return rsi

    def is_high_probability_time(self, current_bar):
        """Check if current time is within high probability trading windows"""
        current_time = pd.to_datetime(self.data.index[current_bar]).time()

        for start_time, end_time in self.high_probability_times:
            if start_time <= current_time <= end_time:
                return True

        return False

    def analyze_market_condition(self, current_bar):
        """Analyze market condition to determine trend and volatility"""
        # Determine trend direction
        trend = 0

        # Strong trend if all EMAs aligned
        if self.ema5[current_bar] > self.ema10[current_bar] > self.ema20[current_bar]:
            # Strong uptrend
            trend = 2
        elif self.ema5[current_bar] < self.ema10[current_bar] < self.ema20[current_bar]:
            # Strong downtrend
            trend = -2
        elif self.ema5[current_bar] > self.ema10[current_bar]:
            # Moderate uptrend
            trend = 1
        elif self.ema5[current_bar] < self.ema10[current_bar]:
            # Moderate downtrend
            trend = -1

        # Check for trend strength using last 3 candles
        price_action = 0
        if current_bar >= 3:
            if (self.data.Close[current_bar] > self.data.Close[current_bar-1] > self.data.Close[current_bar-2]):
                price_action = 1  # Strong uptrend
            elif (self.data.Close[current_bar] < self.data.Close[current_bar-1] < self.data.Close[current_bar-2]):
                price_action = -1  # Strong downtrend

        # Check RSI for overbought/oversold conditions
        rsi_signal = 0
        if self.rsi[current_bar] < 30:
            rsi_signal = 1  # Oversold - potential buy
        elif self.rsi[current_bar] > 70:
            rsi_signal = -1  # Overbought - potential sell

        # Combine signals
        combined_signal = trend + price_action + rsi_signal

        # Determine volatility level
        volatility = self.atr[current_bar] / self.data.Close[current_bar] * 10000  # Normalized ATR

        return {
            'trend': trend,
            'price_action': price_action,
            'rsi_signal': rsi_signal,
            'combined_signal': combined_signal,
            'volatility': volatility
        }

    def should_close_position(self, current_bar, trade_start_bar, is_long):
        """Determine if a position should be closed based on market conditions"""
        # Calculate how long the position has been open
        bars_open = current_bar - trade_start_bar

        # Get current price and entry price
        current_price = self.data.Close[current_bar]
        entry_price = self.data.Close[trade_start_bar]

        # ULTRA-RAPID PROFIT TAKING
        # Take any profit immediately (even tiny profits)
        if self.position.pl > 0:
            return True

        # Quick loss exit - close losing positions after just 1 bar
        if bars_open >= 1 and self.position.pl < 0:
            return True

        # Close long position if:
        if is_long:
            # 1. Price moves against position by more than 0.5 ATR
            if current_price < entry_price - (self.atr[current_bar] * 0.5):
                return True

        # Close short position if:
        else:
            # 1. Price moves against position by more than 0.5 ATR
            if current_price > entry_price + (self.atr[current_bar] * 0.5):
                return True

        # Always close if position has been open for too long (5 bars max)
        if bars_open >= 5:
            return True

        return False

    def calculate_position_size(self, risk_percent, stop_loss_points, is_high_probability=False):
        """Calculate position size based on risk management"""
        # Calculate risk amount
        equity = self.equity
        risk_amount = equity * (risk_percent / 100)

        # Calculate position size based on risk
        if stop_loss_points == 0:
            return self.base_position_size

        position_size = risk_amount / stop_loss_points

        # Round to standard lot sizes
        position_size = round(position_size)

        # Minimum and maximum position sizes
        min_lot = 1
        max_lot = 5

        position_size = max(min_lot, min(position_size, max_lot))

        # Increase position size for high probability setups
        if is_high_probability:
            position_size = min(position_size * 1.5, max_lot)

        return position_size

    def kelly_position_size(self, win_prob, win_loss_ratio):
        """
        Calculate a Kelly-based position size
        Kelly formula: f* = (p*b - q) / b
        """
        f_star = (win_prob * win_loss_ratio - (1 - win_prob)) / win_loss_ratio
        f_star = max(0, min(f_star, 1))
        size = int(self.equity * f_star / 1000)
        return max(1, min(5, size))  # bound size between 1 and 5

    def predict_7_pips_direction(self):
        # Updated to force a trade direction based on pips difference.
        # Multiplier (e.g., 10000) converts the price difference to pips.
        if len(self.data.Close) > 1:
            pips = (self.data.Close.iloc[-1] - self.data.Close.iloc[-2]) * 10000
            return 1 if pips >= 0 else -1
        return 1

    def calculate_trend_probability(self, lookback=7):
        # Calculate probability from recent pip changes (using 7 bars as proxy for 7 sec)
        if len(self.data.Close) < lookback + 1:
            return 0.5  # neutral probability if insufficient data
        pip_changes = (self.data.Close.diff() * 10000).iloc[-lookback:]
        mean_pip = pip_changes.mean()
        std_pip = pip_changes.std() if pip_changes.std() > 0 else 1
        # We'll use a normal CDF to compute probability that next pip change is positive.
        def norm_cdf(x, mean, std):
            from math import erf, sqrt
            z = (x - mean) / (std * sqrt(2))
            return 0.5 * (1 + erf(z))
        # If current pip diff is positive, probability that trend continues is:
        if pip_changes.iloc[-1] >= 0:
            prob = 1 - norm_cdf(0, mean_pip, std_pip)
        else:
            prob = norm_cdf(0, mean_pip, std_pip)
        return prob

    def next(self):
        current_bar = len(self.data) - 1
        if current_bar < 5:
            return
        market_cond = self.analyze_market_condition(current_bar)
        base_direction = self.predict_7_pips_direction()
        # New: calculate trend probability over pip changes in 7 sec window
        trend_prob = self.calculate_trend_probability(lookback=7)
        # Final direction based on base prediction and probability threshold (e.g., 60%)
        final_direction = base_direction if trend_prob >= 0.6 else -base_direction
        
        # Determine trade open time in seconds using the data's timestamp
        entry_bar = self.trade_start_bars.get(id(self.position), current_bar)
        entry_time = self.data.index[entry_bar]
        current_time = self.data.index[current_bar]
        time_open = (current_time - entry_time).total_seconds()
        
        if not self.position:
            if final_direction > 0:
                self.buy(size=1)
                self.trade_start_bars[id(self.position)] = current_bar
            else:
                self.sell(size=1)
                self.trade_start_bars[id(self.position)] = current_bar
        else:
            # Close position if: profit reached; loss after 2+ bars; or no profit within 7 sec
            if (self.position.pl >= 1.0 or 
                ((current_bar - entry_bar) >= 2 and self.position.pl <= -1.0) or 
                (time_open >= 7 and self.position.pl <= 0)):
                self.last_close_bar = current_bar
                self.position.close()
                if final_direction > 0:
                    self.buy(size=1)
                    self.trade_start_bars[id(self.position)] = current_bar
                else:
                    self.sell(size=1)
                    self.trade_start_bars[id(self.position)] = current_bar
