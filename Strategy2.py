"""
Strategy2: Liquidity Zone Retest & S/R-Based Trading Strategy

Enhancements:
- Integrated FVG data for trade signals.
- Added ATR and RSI filters for better trade validation.
- Improved trade logic with dynamic stop loss and target price calculations.
"""

#***************************
# Import necessary libraries
#***************************

import sys
import os
import importlib.util

# Helper function to dynamically load a module
def load_module(module_name, relative_path):
    module_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), relative_path)
    spec = importlib.util.spec_from_file_location(module_name, module_path)
    module = importlib.util.module_from_spec(spec)
    sys.modules[module_name] = module
    spec.loader.exec_module(module)
    return module

# Load the required modules dynamically
function = load_module("function", "../function.py")
FVG = load_module("FVG", "../FVG.py")
Bos_Choch = load_module("Bos_Choch", "../Bos_Choch.py")
# Load the main module
main = load_module("main", "../main.py")
import MetaTrader5 as mt5
import pandas as pd
from function import calculate_atr, calculate_adx, calculate_rsi
from main import get_historical_data
from backtesting import Strategy
from FVG import FVG
from Bos_Choch import market_structure_fractal
from tabulate import tabulate
from datetime import time as dtime
import pytz

#***************************
# Implementation of the improved Strategy2 for backtesting
#***************************
class Strategy2(Strategy):
    """
    Implements a liquidity zone retest and support/resistance-based strategy.
    Uses strong trend confirmation via ADX & RSI, plus FVG analysis.
    """

    def compute_rsi(self, series: pd.Series, period: int = 14) -> pd.Series:
        # Simple RSI calculation without talib
        delta = series.diff()
        up = delta.clip(lower=0)
        down = -1 * delta.clip(upper=0)
        ema_up = up.ewm(span=period, adjust=False).mean()
        ema_down = down.ewm(span=period, adjust=False).mean()
        rs = ema_up / ema_down
        return 100 - (100 / (1 + rs))

    def init(self):
        """
        Initializes technical indicators and config:
        - EMA20, EMA50, EMA200
        - ATR14, ADX14, RSI14
        - FVG data
        - Flags for trading hours
        """
        self.ema20 = self.I(lambda c: pd.Series(c).ewm(span=20).mean(), self.data.Close)
        self.ema50 = self.I(lambda c: pd.Series(c).ewm(span=50).mean(), self.data.Close)
        self.ema200 = self.I(lambda c: pd.Series(c).ewm(span=200).mean(), self.data.Close)
        self.atr14 = self.I(lambda h, l, c: calculate_atr(pd.DataFrame({'high': h, 'low': l, 'close': c}), period=14),
                              self.data.High, self.data.Low, self.data.Close)
        self.adx14 = self.I(lambda h, l, c: calculate_adx(pd.DataFrame({'high': h, 'low': l, 'close': c}), period=14),
                              self.data.High, self.data.Low, self.data.Close)
        self.rsi14 = self.I(lambda c: self.compute_rsi(pd.Series(c), period=14), self.data.Close)

        # Ensure the DataFrame has the required columns for FVG
        if not hasattr(self, '_processed_data'):
            self._processed_data = self.data.df.copy()
            self._processed_data.rename(columns={'Open': 'open', 'High': 'high', 'Low': 'low', 'Close': 'close'}, inplace=True)
            if 'time' not in self._processed_data.columns:
                self._processed_data['time'] = self._processed_data.index  # Add 'time' column if missing

        # Fetch FVG data
        self.fvg_data = FVG.fvg(self._processed_data)  # Pass the processed DataFrame

        # Initialize support/resistance data
        self._support_resistance_data = pd.DataFrame(columns=["support_break", "resistance_break"])  # Initialize as an empty DataFrame

        self.support_resistance_levels = []
        self.is_trading_time = False  # Initialize trading time flag
         # check Volatile market
        self.is_volatile_market = False


    def next(self):
        """
        Core logic on each new bar:
        - Check if within trading hours
        - Use FVG data for trade signals
        - Apply RSI and ATR filters for validation
        """
        # Ensure _support_resistance_data is initialized
        if not hasattr(self, '_support_resistance_data'):
            self._support_resistance_data = pd.DataFrame(columns=["support_break", "resistance_break"])

        # Ensure trades are only taken during allowed trading hours
        india_tz = pytz.timezone("Asia/Kolkata")
        current_bar_time = self.data.df.index[-1]
        if current_bar_time.tzinfo is None:
            current_bar_time = pytz.utc.localize(current_bar_time)
        current_bar_time = current_bar_time.astimezone(india_tz).time()
        morning_start = dtime(8, 30)
        morning_end = dtime(12, 30)
        evening_start = dtime(17, 30)
        evening_end = dtime(20, 30)

        self.is_trading_time = (morning_start <= current_bar_time <= morning_end) or \
                               (evening_start <= current_bar_time <= evening_end)

        if not self.is_trading_time:
            return

        # Allow trade if ADX is at least 20
        adx_value = self.adx14[-1]
        if adx_value < 20:
            return

        # Fetch the latest FVG data
        fvg_bull = self.fvg_data["is_bull"]
        fvg_bear = self.fvg_data["is_bear"]
        gap_top = self.fvg_data["gap_top"]
        gap_bottom = self.fvg_data["gap_bottom"]

        ema20_last = self.ema20[-1]
        ema50_last = self.ema50[-1]
        ema200_last = self.ema200[-1]
        atr14_last = self.atr14[-1]
        current_price = self.data.Close[-1]
        rsi_last = self.rsi14[-1]
        open_trades = len(self.trades)
        
        # Check volatility on a higher timeframe (e.g., H1)
        data_h1 = self.data.df.copy()
        data_h1.rename(columns={'High': 'high', 'Low': 'low', 'Close': 'close'}, inplace=True)
        data_h1 = data_h1.resample('1H').agg({'high': 'max', 'low': 'min', 'close': 'last'}).dropna()
        if not data_h1.empty:
            atr_h1 = calculate_atr(data_h1, period=14)
            atr_ema_h1 = atr_h1.ewm(span=20).mean()
            self.is_volatile_market = atr_h1.iloc[-1] > atr_ema_h1.iloc[-1]
        else:
            self.is_volatile_market = False



        # # Volatility filter: only trade if current ATR is above its 20-bar moving average
        # if len(self.atr14) >= 20:
        #     atr_ma = pd.Series(self.atr14[-20:]).mean()
        #     if atr14_last < atr_ma:
        #         return

        trend_bias = "BULLISH" if ema20_last > ema200_last else "BEARISH"

        # Ensure the DataFrame has the required columns
        if not hasattr(self, '_processed_data'):
            self._processed_data = self.data.df.copy()
            self._processed_data.rename(columns={'Open': 'open', 'High': 'high', 'Low': 'low', 'Close': 'close'},
                                        inplace=True)
            self._processed_data['time'] = self._processed_data.index

       
        # Iterate over FVG signals
        for i in range(len(fvg_bull)):
            if fvg_bull[i] and trend_bias == "BULLISH" and rsi_last > 55 and current_price > gap_top[i] and self.is_trading_time and open_trades==0 and self.ema20[-1]>self.ema50[-1] and self.is_volatile_market :
                # Bullish FVG trade
                open_trades += 1
                print(open_trades)
                entry_price = current_price
                stop_loss = entry_price - 2.2 * atr14_last
                target_price = entry_price + 6.2 * atr14_last
                fix = round(abs( entry_price - stop_loss ) / 10)
                if stop_loss < entry_price < target_price:
                    self.buy(size=fix, sl=stop_loss, tp=target_price)
            elif fvg_bear[i] and trend_bias == "BEARISH" and rsi_last < 45 and current_price < gap_bottom[i] and self.is_trading_time and open_trades==0 and self.ema20[-1]<self.ema50[-1] and self.is_volatile_market:
                # Bearish FVG trade
                open_trades += 1
                print(open_trades)
                entry_price = current_price
                stop_loss = entry_price + 2.2 * atr14_last
                target_price = entry_price - 6.2 * atr14_last
                fix = round(abs( entry_price - stop_loss ) / 10)
                if target_price < entry_price < stop_loss:
                    self.sell(size=fix, sl=stop_loss, tp=target_price)

        # Print trade table if any trades are executed
        table_data = []
        if hasattr(self, 'trades') and self.trades:
            for trade in self.trades:
                # Removed 'trade.signal' as it does not exist
                table_data.append(["Strategy 2 (Trade)", trade.entry_price, trade.sl, trade.tp,trade.size])
            print(tabulate(table_data, headers=["Type", "Entry", "SL", "TP","Lot Size"], tablefmt="grid"))


    def run(self):
        """
        Kicks off the strategy with trailing stop management.
        """
        super().run()
        if hasattr(self, '_manage_trade_callback'):
            self._manage_trade_callback()