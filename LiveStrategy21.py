import MetaTrader5 as mt5
import pandas as pd
import time
from datetime import datetime, timedelta
from tabulate import tabulate

symbol = "Boom 1000 Index"
lot_size = 0.2  # Updated as per requirements

trade_history = []

def get_5min_ohlc(symbol, bars=10):
    rates = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_M5, 0, bars)
    if rates is None or len(rates) < bars:
        return None
    df = pd.DataFrame(rates)
    df['Datetime'] = pd.to_datetime(df['time'], unit='s')
    df.set_index('Datetime', inplace=True)
    return df

def find_last_bullish_spike(df):
    # Find the last bullish candle (body > 4 points) in the DataFrame (excluding current forming candle)
    for idx in reversed(df.index):
        row = df.loc[idx]
        body = row['close'] - row['open']
        if row['close'] > row['open']:
            return {'Datetime': idx, 'Spike_Open': row['open'], 'Spike_Close': row['close']}
    return None

def get_current_price():
    tick = mt5.symbol_info_tick(symbol)
    return tick.bid if tick else None

def open_long(lot, price):
    req = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": lot,
        "type": mt5.ORDER_TYPE_BUY,
        "price": price,
        "deviation": 10,
        "magic": 234001,
        "comment": "SpikeLong",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_FOK,  # <-- changed here
    }
    res = mt5.order_send(req)
    if res and res.retcode == mt5.TRADE_RETCODE_DONE:
        return res.order
    return None

def open_short(lot, price, tp):
    req = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": lot,
        "type": mt5.ORDER_TYPE_SELL,
        "price": price,
        "tp": tp,
        "deviation": 10,
        "magic": 234001,
        "comment": "SpikeShort",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_FOK,  # <-- changed here
    }
   
    res = mt5.order_send(req)
    if res and res.retcode == mt5.TRADE_RETCODE_DONE:
        return res.order
    # Debug print if order failed
    print(f"Order send failed: {res}")
    print(f"MT5 last error: {mt5.last_error()}")
    return None

def close_position(ticket):
    positions = mt5.positions_get(ticket=ticket)
    if not positions:
        return False
    pos = positions[0]
    price = get_current_price()
    req = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": pos.volume,
        "type": mt5.ORDER_TYPE_SELL,
        "position": ticket,
        "price": price,
        "deviation": 10,
        "magic": 234001,
        "comment": "SpikeClose",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_FOK,  # <-- changed here
    }
    res = mt5.order_send(req)
    return res and res.retcode == mt5.TRADE_RETCODE_DONE

def get_closed_pnl(ticket):
    deals = mt5.history_deals_get(position=ticket)
    if deals:
        return sum(d.profit for d in deals)
    return 0

def print_trade_table():
    if not trade_history:
        print("No trades yet.")
        return
    headers = ["#", "Entry Time", "Entry Price", "Exit Time", "Exit Price", "PnL"]
    data = []
    for i, t in enumerate(trade_history):
        data.append([
            i+1, t['entry_time'], t['entry_price'],
            t['exit_time'], t['exit_price'], round(t['pnl'], 2)
        ])
    print(tabulate(data, headers=headers, tablefmt="fancy_grid"))

def is_avoid_hour():
    df = get_5min_ohlc(symbol, 1)
    if df is None or df.empty:
        now = datetime.now()
    else:
        now = df.index[-1]
   
    return 16 <= now.hour < 20

if __name__ == "__main__":
    if not mt5.initialize():
        print("MT5 initialization failed")
        print(f"Error code: {mt5.last_error()[0]}, Description: {mt5.last_error()[1]}")
        exit()
  
    last_spike = None
    open_trade = None

    while True:
      
        if is_avoid_hour():
            time.sleep(1)
            continue
       
        df = get_5min_ohlc(symbol, 11)  # 10 completed bars + current forming
        
        if df is None or len(df) < 3:
            time.sleep(5)
            continue
       

        # Fetch last 200 candles for spike detection
        spike_df = get_5min_ohlc(symbol, 200)
        if spike_df is None or len(spike_df) < 2:
            print("Not enough data for spike detection.")
            time.sleep(2)
            continue
        hist_spike_df = spike_df.iloc[:-1]
        last_spike = find_last_bullish_spike(hist_spike_df)
    
      
        if not last_spike:
            time.sleep(2)
            continue

        spike_time = last_spike['Datetime']
        spike_open = last_spike['Spike_Open']
        spike_close = last_spike['Spike_Close']

        now = datetime.now()
        # Entry conditions (Short)
        if not open_trade:
            # At least 20 seconds since spike
            if (now - spike_time).total_seconds() < 20:
                time.sleep(2)
                continue

            # Get previous and current bar (relative to current forming bar)
            prev_bar = df.iloc[-2]
            curr_bar = df.iloc[-1]
            prev_low = prev_bar['low']
            curr_low = curr_bar['low']
            curr_close = curr_bar['close']
            print(curr_low < spike_open)
            print(prev_low >= spike_open)
            print(curr_close < spike_open)
            # Cross and close confirmation
            if curr_low < spike_open and curr_close < spike_open:
           
                print("Trade is active ")
                entry_price = get_current_price()
                entry_time = now
                entry_bar_idx = df.index[-1]
                # Calculate TP for short position
                spike_body = spike_close - spike_open
                ads_pips = 0.5 * spike_body
                tp_price = entry_price - ads_pips
                ticket = open_short(lot_size, entry_price, tp=tp_price)
                if ticket:
                    print(f"Opened SHORT at {entry_price} (ticket {ticket}) after cross below spike open at {spike_open}, TP set at {tp_price}")
                    open_trade = {
                        'ticket': ticket,
                        'entry_time': entry_time,
                        'entry_price': entry_price,
                        'spike_open': spike_open,
                        'spike_close': spike_close,
                        'entry_bar_idx': entry_bar_idx,
                        'prev_bar_high': prev_bar['high'],
                        'tp_price': tp_price
                    }
                else:
                    print(f"Failed to open SHORT at {entry_price} after cross below spike open at {spike_open}")
                    time.sleep(2)
                    continue

        # Exit conditions
        if open_trade:
            # Monitor each new 1-min bar
            df = get_5min_ohlc(symbol, 3)
            if df is None or len(df) < 2:
                time.sleep(2)
                continue
            curr_bar = df.iloc[-1]
            curr_high = curr_bar['high']
            curr_close = curr_bar['close']

            # Reversal breakout
            if curr_high > open_trade['prev_bar_high']:
                ticket = open_trade['ticket']
                exit_price = get_current_price()
                closed = close_position(ticket)
                time.sleep(2)
                pnl = get_closed_pnl(ticket)
                trade_history.append({
                    'entry_time': open_trade['entry_time'].strftime("%Y-%m-%d %H:%M:%S"),
                    'entry_price': open_trade['entry_price'],
                    'exit_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'exit_price': exit_price,
                    'pnl': pnl
                })
                print("Exit: Reversal breakout")
                print_trade_table()
                open_trade = None
                continue

            # Profit target
            spike_body = open_trade['spike_close'] - open_trade['spike_open']
            ads_pips = 0.5 * spike_body
            profit_move = open_trade['entry_price'] - curr_close
            print(f"Profit move: {profit_move}, ADS pips: {ads_pips}, TP: {open_trade.get('tp_price')}")
            if profit_move >= ads_pips:
                ticket = open_trade['ticket']
                exit_price = get_current_price()
                closed = close_position(ticket)
                time.sleep(2)
                pnl = get_closed_pnl(ticket)
                trade_history.append({
                    'entry_time': open_trade['entry_time'].strftime("%Y-%m-%d %H:%M:%S"),
                    'entry_price': open_trade['entry_price'],
                    'exit_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'exit_price': exit_price,
                    'pnl': pnl
                })
                print("Exit: Profit target hit")
                print_trade_table()
                open_trade = None
                continue

        time.sleep(2)
