import sys
import os
import importlib.util
import time
import logging
import MetaTrader5 as mt5
import pandas as pd
from backtesting import Strategy
from datetime import time as dtime
from function import calculate_atr, calculate_adx, calculate_rsi
from main import get_historical_data, send_order

class Strategy5(Strategy):
   

    def init(self):
        # Fetch minimal bars for quick checks
        self.symbol = "XAUUSD"
        self.is_trading_time = False
        self.lot = 0.03
        self.ema20 = self.I(lambda c: pd.Series(c).ewm(span=9).mean(), self.data.Close)
        

    def next(self):
        # Use the timestamp of the current candle instead of local machine time
        current_ts = self.data.index[-1]
        current_dt = dtime(current_ts.hour, current_ts.minute)

        # Expand or remove this time check so trades actually trigger with your data
        if not ((dtime(8, 30) <= current_dt <= dtime(12, 30)) or (dtime(17, 30) <= current_dt <= dtime(20, 30))):
            self.is_trading_time = False
            return
        self.is_trading_time = True

        current_price = self.data.Close[-1]
        # Remove real mt5 position checks; use backtesting library's self.position
        if self.position:
            # Check strategy profit via self.position.pl
            if self.position.pl > 1000:
                self.position.close()
            return

        if current_price > self.ema20[-1] and self.is_trading_time:
            stop_loss = current_price - 1000 * 0.01  # For example
            target_price = current_price + 1000 * 0.01
            self.buy(size=1, sl=stop_loss, tp=target_price)
        else:
            stop_loss = current_price + 1000 * 0.01
            target_price = current_price - 1000 * 0.01
            self.sell(size=1, sl=stop_loss, tp=target_price)

    def run(self):
        """
        Runs the strategy and manages trades with trailing stop mechanism.
        """
        super().run()