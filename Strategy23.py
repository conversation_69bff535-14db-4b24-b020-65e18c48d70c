import MetaTrader5 as mt5
import pandas as pd
import time
from datetime import datetime, timedelta
from tabulate import tabulate

symbol = "Crash 300 Index"
lot_size = 0.5  # Adjust as needed

trade_history = []

def get_1min_ohlc(symbol, bars=10):
    rates = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_M5, 0, bars)
    if rates is None or len(rates) < bars:
        return None
    df = pd.DataFrame(rates)
    df['Datetime'] = pd.to_datetime(df['time'], unit='s')
    df.set_index('Datetime', inplace=True)
    return df

def find_last_bearish_candle(df):
    # Find the last bearish candle with body > 4 in the DataFrame (excluding current forming candle)
    for idx in reversed(df.index):
        row = df.loc[idx]
        body = row['open'] - row['close']
        if row['close'] < row['open']:
            return {'Datetime': idx, 'Spike_Open': row['open'], 'Spike_Close': row['close']}
    return None

def get_current_price():
    tick = mt5.symbol_info_tick(symbol)
    return tick.bid if tick else None

def get_min_tp_distance(symbol):
    info = mt5.symbol_info(symbol)
    if info is None:
        return None
    # trade_stops_level is in points, convert to price
    return info.trade_stops_level * info.point

def open_long(lot, price, tp=None):
    if tp is not None:
        min_tp_dist = get_min_tp_distance(symbol)
        if min_tp_dist is not None:
            if tp - price < min_tp_dist:
                tp = price + min_tp_dist + 0.1 * min_tp_dist  # Add a small buffer
    req = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": lot,
        "type": mt5.ORDER_TYPE_BUY,
        "price": price,
        "deviation": 10,
        "magic": 234001,
        "comment": "SpikeLong",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }
    if tp is not None:
        req["tp"] = tp
    res = mt5.order_send(req)
    if res and res.retcode == mt5.TRADE_RETCODE_DONE:
        return res.order
    return None

def close_position(ticket):
    positions = mt5.positions_get(ticket=ticket)
    if not positions:
        return False
    pos = positions[0]
    price = get_current_price()
    req = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": pos.volume,
        "type": mt5.ORDER_TYPE_SELL,
        "position": ticket,
        "price": price,
        "deviation": 10,
        "magic": 234001,
        "comment": "SpikeClose",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_IOC,
    }
    res = mt5.order_send(req)
    return res and res.retcode == mt5.TRADE_RETCODE_DONE

def get_closed_pnl(ticket):
    deals = mt5.history_deals_get(position=ticket)
    if deals:
        return sum(d.profit for d in deals)
    return 0

def print_trade_table():
    if not trade_history:
        print("No trades yet.")
        return
    headers = ["#", "Entry Time", "Entry Price", "Exit Time", "Exit Price", "PnL"]
    data = []
    for i, t in enumerate(trade_history):
        data.append([
            i+1, t['entry_time'], t['entry_price'],
            t['exit_time'], t['exit_price'], round(t['pnl'], 2)
        ])
    print(tabulate(data, headers=headers, tablefmt="fancy_grid"))

def get_contract_size(symbol):
    info = mt5.symbol_info(symbol)
    if info is not None:
        return info.volume_min  # or info.volume_step or info.contract_size, depending on broker
    return 1

def backtest_strategy():
    print("Running backtest for last 5 days...")
    # Fetch last 5 days of 1-min data
    utc_to = datetime.now()
    utc_from = utc_to - timedelta(days=90)
    if not mt5.initialize():
        print("MT5 initialization failed")
        return
    contract_size = get_contract_size(symbol)
    print(f"Contract size for {symbol}: {contract_size}")
    rates = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_M5, utc_from, utc_to)
    mt5.shutdown()
    if rates is None or len(rates) == 0:
        print("No data retrieved from MT5.")
        return
    df = pd.DataFrame(rates)
    df['Datetime'] = pd.to_datetime(df['time'], unit='s')
    df.set_index('Datetime', inplace=True)
    open_trade = None
    trade_history_bt = []
    capital = 30  # Starting capital in dollars

    for i in range(2, len(df)):
        # Use all candles up to previous one to find last bearish
        df_slice = df.iloc[:i]
        last_spike = find_last_bearish_candle(df_slice.iloc[:-1])
        current_row = df.iloc[i]
        prev_row = df.iloc[i-1]
        now = current_row.name
        # Entry: cross above and close above last bearish candle open, and previous high did not cross
        if last_spike and not open_trade:
            prev_spike_open = last_spike['Spike_Open']
            prev_spike_close = last_spike['Spike_Close']
            prev_spike_time = last_spike['Datetime']
            ads_pips = 0.5 * abs(prev_spike_open - prev_spike_close)
            if ads_pips == 0:
                continue  # Avoid division by zero or zero-hold
            if now - prev_spike_time > timedelta(seconds=20):
                crossed =  (current_row['high'] > prev_spike_open)
                closed_above = current_row['close'] > prev_spike_open
                prev_high_crossed = prev_row['high'] > prev_spike_open
                if crossed and closed_above and not prev_high_crossed:
                    entry_price = prev_spike_open
                    entry_time = now
                    open_trade = {
                        'entry_time': entry_time,
                        'entry_price': entry_price,
                        'ads_pips': ads_pips,
                        'prev_candle_low': prev_row['low'],
                        'entry_idx': i
                    }
        # Exit condition: close if low below previous candle low or price moves by ads_pips in your favor
        if open_trade:
            exit_trade = False
            exit_reason = ""
            # If current low < previous candle low at entry, close trade
            if current_row['low'] < open_trade['prev_candle_low']:
                exit_trade = True
                exit_reason = "Low crossed below previous candle low"
            # else:
            #     # Check if price moved in favor by ads_pips (LONG: current close - entry >= ads_pips)
            #     price_move = current_row['close'] - open_trade['entry_price']
            #     if price_move >= open_trade['ads_pips']:
            #         exit_trade = True
            #         exit_reason = f"ADS pip target reached ({open_trade['ads_pips']:.2f})"
            if exit_trade:
                exit_price = current_row['close']
                # FIX: Use correct contract size and direction for LONG
                pnl = (exit_price - open_trade['entry_price']) * lot_size * contract_size
                taxed_pnl = pnl - 0.1  # Apply tax
                capital += taxed_pnl
                trade_history_bt.append({
                    'entry_time': open_trade['entry_time'].strftime("%Y-%m-%d %H:%M:%S"),
                    'entry_price': open_trade['entry_price'],
                    'exit_time': now.strftime("%Y-%m-%d %H:%M:%S"),
                    'exit_price': exit_price,
                    'pnl': pnl,
                    'taxed_pnl': taxed_pnl,
                    'exit_reason': exit_reason,
                    'capital_after_trade': round(capital, 2)
                })
                open_trade = None
                if capital <= 0:
                    print(f"Capital depleted. Stopping backtest at {now.strftime('%Y-%m-%d %H:%M:%S')}")
                    break
    # Print summary
    if not trade_history_bt:
        print("No trades triggered in backtest.")
    else:
        headers = ["#", "Entry Time", "Entry Price", "Exit Time", "Exit Price", "PnL", "Taxed PnL", "Exit Reason"]
        data = []
        for i, t in enumerate(trade_history_bt):
            data.append([
                i+1, t['entry_time'], t['entry_price'],
                t['exit_time'], t['exit_price'], round(t['pnl'], 2), round(t['taxed_pnl'], 2), t.get('exit_reason', '')
            ])
        print(tabulate(data, headers=headers, tablefmt="fancy_grid"))
        total_pnl = sum(t['pnl'] for t in trade_history_bt)
        total_taxed_pnl = sum(t['taxed_pnl'] for t in trade_history_bt)
        print(f"\nTotal Trades: {len(trade_history_bt)} | Total PnL: {total_pnl:.2f} | Net PnL after tax: {total_taxed_pnl:.2f}")

        # Print table of negative PnL trades
        negative_trades = [t for t in trade_history_bt if t['pnl'] < 0]
        if negative_trades:
            print("\nTrades with Negative PnL:")
            neg_data = []
            for i, t in enumerate(negative_trades):
                neg_data.append([
                    i+1, t['entry_time'], t['entry_price'],
                    t['exit_time'], t['exit_price'], round(t['pnl'], 2), round(t['taxed_pnl'], 2), t.get('exit_reason', '')
                ])
            print(tabulate(neg_data, headers=headers, tablefmt="fancy_grid"))
        else:
            print("\nNo trades with negative PnL.")

        # Print net PnL for each day
        print("\nNet PnL for each day:")
        for t in trade_history_bt:
            t['exit_date'] = t['exit_time'][:10]
        daily_pnl = {}
        daily_taxed_pnl = {}
        for t in trade_history_bt:
            date = t['exit_date']
            daily_pnl[date] = daily_pnl.get(date, 0) + t['pnl']
            daily_taxed_pnl[date] = daily_taxed_pnl.get(date, 0) + t['taxed_pnl']
        daily_data = []
        for date in sorted(daily_pnl.keys()):
            daily_data.append([date, round(daily_pnl[date], 2), round(daily_taxed_pnl[date], 2)])
        print(tabulate(daily_data, headers=["Date", "Net PnL", "Net Taxed PnL"], tablefmt="fancy_grid"))

        # Save daily_data to CSV
        import csv
        with open("daily_pnl.csv", "w", newline="") as f:
            writer = csv.writer(f)
            writer.writerow(["Date", "Net PnL", "Net Taxed PnL"])
            writer.writerows(daily_data)

        # --- 4-hour interval analysis for negative PnL ---
        # Add 4-hour interval label to each negative trade
        for t in negative_trades:
            exit_dt = datetime.strptime(t['exit_time'], "%Y-%m-%d %H:%M:%S")
            interval_start = (exit_dt.hour // 4) * 4
            interval_label = f"{interval_start:02d}:00-{(interval_start+4)%24:02d}:00"
            t['4h_interval'] = interval_label
            t['exit_date'] = exit_dt.date()

        # Table: total negative pnl per 4-hour interval (all days combined)
        neg_pnl_by_interval = {}
        neg_taxed_pnl_by_interval = {}
        for t in negative_trades:
            key = t['4h_interval']
            neg_pnl_by_interval[key] = neg_pnl_by_interval.get(key, 0) + t['pnl']
            neg_taxed_pnl_by_interval[key] = neg_taxed_pnl_by_interval.get(key, 0) + t['taxed_pnl']
        neg_pnl_table = []
        for interval in sorted(neg_pnl_by_interval.keys()):
            neg_pnl_table.append([interval, round(neg_pnl_by_interval[interval], 2), round(neg_taxed_pnl_by_interval[interval], 2)])
        print("\nTotal Negative PnL by 4-hour Interval (all days):")
        print(tabulate(neg_pnl_table, headers=["4H Interval", "Total Negative PnL", "Total Negative Taxed PnL"], tablefmt="fancy_grid"))

        # Build day_interval_neg before using it
        day_interval_neg = {}
        for t in negative_trades:
            day = t['exit_date']
            interval = t['4h_interval']
            day_interval_neg.setdefault(day, {})
            day_interval_neg[day][interval] = day_interval_neg[day].get(interval, 0) + t['pnl']

        # For each day, find the interval with max negative pnl
        max_neg_intervals_per_day = {}
        for day, interval_dict in day_interval_neg.items():
            if interval_dict:
                min_interval = min(interval_dict.items(), key=lambda x: x[1])  # Most negative
                max_neg_intervals_per_day[day] = min_interval[0]

        # Find the 4h interval(s) common to all days as max negative
        from collections import Counter
        interval_counter = Counter(max_neg_intervals_per_day.values())
        common_intervals = [interval for interval, count in interval_counter.items() if count == len(max_neg_intervals_per_day)]

        if common_intervals:
            print("\n4-hour interval(s) with maximum negative PnL common to all days:")
            print(tabulate([[iv] for iv in common_intervals], headers=["4H Interval"], tablefmt="fancy_grid"))
        else:
            print("\nNo single 4-hour interval with maximum negative PnL common to all days.")

        # Table: total negative pnl (sum of all negative trades)
        total_negative_pnl = sum(t['pnl'] for t in negative_trades)
        total_negative_taxed_pnl = sum(t['taxed_pnl'] for t in negative_trades)
        print(f"\nTotal Negative PnL (all trades): {round(total_negative_pnl, 2)} | Total Negative Taxed PnL: {round(total_negative_taxed_pnl, 2)}")
        # --- Exclude trades in common negative 4h interval(s) and recalculate net PnL ---
        if common_intervals:
            # Exclude trades whose exit_time falls in any of the common_intervals
            filtered_trades = [t for t in trade_history_bt if not (
                'exit_time' in t and
                f"{(datetime.strptime(t['exit_time'], '%Y-%m-%d %H:%M:%S').hour // 4) * 4:02d}:00-{((datetime.strptime(t['exit_time'], '%Y-%m-%d %H:%M:%S').hour // 4) * 4 + 4)%24:02d}:00"
                in common_intervals
            )]
            filtered_total_pnl = sum(t['pnl'] for t in filtered_trades)
            filtered_total_taxed_pnl = sum(t['taxed_pnl'] for t in filtered_trades)
            print(f"\nNet PnL after excluding trades in common negative 4H interval(s): {round(filtered_total_pnl, 2)} | Net Taxed PnL: {round(filtered_total_taxed_pnl, 2)}")

            # Net PnL for each day after exclusion
            print("\nNet PnL for each day (excluding common negative 4H interval):")
            daily_pnl_excl = {}
            daily_taxed_pnl_excl = {}
            for t in filtered_trades:
                date = t['exit_time'][:10]
                daily_pnl_excl[date] = daily_pnl_excl.get(date, 0) + t['pnl']
                daily_taxed_pnl_excl[date] = daily_taxed_pnl_excl.get(date, 0) + t['taxed_pnl']
            daily_data_excl = []
            for date in sorted(daily_pnl_excl.keys()):
                daily_data_excl.append([date, round(daily_pnl_excl[date], 2), round(daily_taxed_pnl_excl[date], 2)])
            print(tabulate(daily_data_excl, headers=["Date", "Net PnL (Excl. Neg 4H)", "Net Taxed PnL (Excl. Neg 4H)"], tablefmt="fancy_grid"))
        else:
            print("\nNo common 4-hour interval to exclude for recalculation.")

        # Exclude trades in all 4-hour intervals (exclude all intervals in the table above)
        exclude_intervals = [
            "00:00-04:00",
            "04:00-08:00",
            "08:00-12:00",
            "12:00-16:00",
            "16:00-20:00",
            "20:00-00:00"
        ]
        filtered_trades_all_excluded = [t for t in trade_history_bt if not (
            'exit_time' in t and
            f"{(datetime.strptime(t['exit_time'], '%Y-%m-%d %H:%M:%S').hour // 4) * 4:02d}:00-{((datetime.strptime(t['exit_time'], '%Y-%m-%d %H:%M:%S').hour // 4) * 4 + 4)%24:02d}:00"
            in exclude_intervals
        )]
        filtered_total_pnl_all_excluded = sum(t['pnl'] for t in filtered_trades_all_excluded)
        filtered_total_taxed_pnl_all_excluded = sum(t['taxed_pnl'] for t in filtered_trades_all_excluded)
        print(f"\nNet PnL after excluding ALL 4H intervals listed above: {round(filtered_total_pnl_all_excluded, 2)} | Net Taxed PnL: {round(filtered_total_taxed_pnl_all_excluded, 2)}")

        # Net PnL for each day after exclusion
        print("\nNet PnL for each day (excluding ALL 4H intervals listed above):")
        daily_pnl_excl_all = {}
        daily_taxed_pnl_excl_all = {}
        for t in filtered_trades_all_excluded:
            date = t['exit_time'][:10]
            daily_pnl_excl_all[date] = daily_pnl_excl_all.get(date, 0) + t['pnl']
            daily_taxed_pnl_excl_all[date] = daily_taxed_pnl_excl_all.get(date, 0) + t['taxed_pnl']
        daily_data_excl_all = []
        for date in sorted(daily_pnl_excl_all.keys()):
            daily_data_excl_all.append([date, round(daily_pnl_excl_all[date], 2), round(daily_taxed_pnl_excl_all[date], 2)])
        print(tabulate(daily_data_excl_all, headers=["Date", "Net PnL (Excl. All 4H)", "Net Taxed PnL (Excl. All 4H)"], tablefmt="fancy_grid"))

        # --- Find 4-hour intervals with NO trades for each day ---
        from collections import defaultdict
        # Build a set of all possible 4-hour intervals
        all_intervals = [
            "00:00-04:00",
            "04:00-08:00",
            "08:00-12:00",
            "12:00-16:00",
            "16:00-20:00",
            "20:00-00:00"
        ]
        # Map: date -> set of intervals with trades
        day_intervals_with_trade = defaultdict(set)
        for t in trade_history_bt:
            exit_dt = datetime.strptime(t['exit_time'], "%Y-%m-%d %H:%M:%S")
            interval_start = (exit_dt.hour // 4) * 4
            interval_label = f"{interval_start:02d}:00-{(interval_start+4)%24:02d}:00"
            day_intervals_with_trade[exit_dt.date()].add(interval_label)
        # For each day, get intervals with NO trades
        day_no_spike_intervals = {}
        for day, intervals in day_intervals_with_trade.items():
            no_spike = set(all_intervals) - intervals
            day_no_spike_intervals[day] = no_spike
        # Group by weekday
        weekday_no_spike = defaultdict(list)
        for day, intervals in day_no_spike_intervals.items():
            weekday = datetime.strptime(str(day), "%Y-%m-%d").strftime("%A")
            weekday_no_spike[weekday].append(intervals)
        # For each weekday, find intervals that are common to all days of that weekday
        weekday_common_no_spike = {}
        for weekday, list_of_sets in weekday_no_spike.items():
            if list_of_sets:
                common = set.intersection(*list_of_sets)
                weekday_common_no_spike[weekday] = sorted(list(common))
            else:
                weekday_common_no_spike[weekday] = []
        # Print table
        print("\nCommon No Spike 4H Intervals for Each Weekday:")
        no_spike_table = []
        for weekday in ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]:
            intervals = weekday_common_no_spike.get(weekday, [])
            no_spike_table.append([weekday, ", ".join(intervals) if intervals else "None"])
        print(tabulate(no_spike_table, headers=["Weekday", "Common No Spike 4H Interval(s)"], tablefmt="fancy_grid"))

        # --- Find maximum continuous no spike interval (in hours) for each day and weekday ---
        # For each day, build a binary list for 4h intervals: 1 if no spike, 0 if spike
        interval_order = [
            "00:00-04:00",
            "04:00-08:00",
            "08:00-12:00",
            "12:00-16:00",
            "16:00-20:00",
            "20:00-00:00"
        ]
        day_max_no_spike = {}
        day_max_no_spike_intervals = {}
        for day, no_spike_set in day_no_spike_intervals.items():
            # Build binary list
            binary = [1 if iv in no_spike_set else 0 for iv in interval_order]
            # Find max continuous 1's
            max_len = 0
            max_start = 0
            curr_len = 0
            curr_start = 0
            for i, val in enumerate(binary):
                if val == 1:
                    if curr_len == 0:
                        curr_start = i
                    curr_len += 1
                    if curr_len > max_len:
                        max_len = curr_len
                        max_start = curr_start
                else:
                    curr_len = 0
            # Save max interval and its length (in hours)
            if max_len > 0:
                start_iv = interval_order[max_start]
                end_idx = max_start + max_len - 1
                end_iv = interval_order[end_idx]
                # Calculate start and end hour
                start_hour = int(start_iv[:2])
                end_hour = (int(end_iv[:2]) + 4) % 24
                interval_label = f"{start_hour:02d}:00-{end_hour:02d}:00"
                day_max_no_spike[day] = max_len * 4
                day_max_no_spike_intervals[day] = interval_label
            else:
                day_max_no_spike[day] = 0
                day_max_no_spike_intervals[day] = None
        # Group by weekday
        weekday_max_no_spike = {}
        weekday_max_no_spike_interval = {}
        from collections import defaultdict
        weekday_day_map = defaultdict(list)
        for day in day_max_no_spike:
            weekday = datetime.strptime(str(day), "%Y-%m-%d").strftime("%A")
            weekday_day_map[weekday].append(day)
        for weekday, days in weekday_day_map.items():
            # Find the minimum max no spike hours across all days of this weekday
            min_x = min(day_max_no_spike[day] for day in days)
            # Find the interval(s) that have this min_x for all days
            intervals = [day_max_no_spike_intervals[day] for day in days if day_max_no_spike[day] == min_x and day_max_no_spike_intervals[day] is not None]
            # Find common intervals
            from collections import Counter
            interval_counts = Counter(intervals)
            common_intervals = [iv for iv, count in interval_counts.items() if count == len(days)] if intervals else []
            weekday_max_no_spike[weekday] = min_x
            weekday_max_no_spike_interval[weekday] = common_intervals
        # Print table
        print("\nMaximum Continuous No Spike Interval (in hours) for Each Weekday:")
        max_no_spike_table = []
        for weekday in ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]:
            x = weekday_max_no_spike.get(weekday, 0)
            intervals = weekday_max_no_spike_interval.get(weekday, [])
            max_no_spike_table.append([weekday, x, ", ".join(intervals) if intervals else "None"])
        print(tabulate(max_no_spike_table, headers=["Weekday", "Max No Spike Hours (x)", "Interval(s)"] , tablefmt="fancy_grid"))

if __name__ == "__main__":
    backtest_strategy()