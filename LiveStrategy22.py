import MetaTrader5 as mt5
import pandas as pd
import time
from datetime import datetime, timedelta
from tabulate import tabulate

symbol = "Crash 1000 Index"
lot_size = 0.2

trade_history = []

def get_5min_ohlc(symbol, bars=10):
    rates = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_M5, 0, bars)
    if rates is None or len(rates) < bars:
        return None
    df = pd.DataFrame(rates)
    df['Datetime'] = pd.to_datetime(df['time'], unit='s')
    df.set_index('Datetime', inplace=True)
    return df

def find_last_bearish_spike(df):
    for idx in reversed(df.index):
        row = df.loc[idx]
        body = row['open'] - row['close']
        if row['close'] < row['open']:
            return {'Datetime': idx, 'Spike_Open': row['open'], 'Spike_Close': row['close']}
    return None

def get_current_price():
    tick = mt5.symbol_info_tick(symbol)
    return tick.bid if tick else None

def get_min_tp_distance(symbol):
    info = mt5.symbol_info(symbol)
    if info is None:
        return None
    return info.trade_stops_level * info.point

def open_long(lot, price, tp=None):
    if tp is not None:
        min_tp_dist = get_min_tp_distance(symbol)
        if min_tp_dist is not None:
            if tp - price < min_tp_dist:
                tp = price + min_tp_dist + 0.1 * min_tp_dist
    req = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": lot,
        "type": mt5.ORDER_TYPE_BUY,
        "price": price,
        "deviation": 10,
        "magic": 234001,
        "comment": "SpikeLong",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_FOK,
    }
    if tp is not None:
        req["tp"] = tp
    res = mt5.order_send(req)
    if res and res.retcode == mt5.TRADE_RETCODE_DONE:
        return res.order
    print(f"Order send failed: {res}")
    print(f"MT5 last error: {mt5.last_error()}")
    return None

def close_position(ticket):
    positions = mt5.positions_get(ticket=ticket)
    if not positions:
        return False
    pos = positions[0]
    price = get_current_price()
    req = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": pos.volume,
        "type": mt5.ORDER_TYPE_SELL,
        "position": ticket,
        "price": price,
        "deviation": 10,
        "magic": 234001,
        "comment": "SpikeClose",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_FOK,
    }
    res = mt5.order_send(req)
    return res and res.retcode == mt5.TRADE_RETCODE_DONE

def get_closed_pnl(ticket):
    deals = mt5.history_deals_get(position=ticket)
    if deals:
        return sum(d.profit for d in deals)
    return 0

def print_trade_table():
    if not trade_history:
        print("No trades yet.")
        return
    headers = ["#", "Entry Time", "Entry Price", "Exit Time", "Exit Price", "PnL"]
    data = []
    for i, t in enumerate(trade_history):
        data.append([
            i+1, t['entry_time'], t['entry_price'],
            t['exit_time'], t['exit_price'], round(t['pnl'], 2)
        ])
    print(tabulate(data, headers=headers, tablefmt="fancy_grid"))

if __name__ == "__main__":
    if not mt5.initialize():
        print("MT5 initialization failed")
        print(f"Error code: {mt5.last_error()[0]}, Description: {mt5.last_error()[1]}")
        exit()

    open_trade = None
    capital = 30  # Starting capital in dollars (for tracking, not used in live)

    while True:
        df = get_5min_ohlc(symbol, 11)
        if df is None or len(df) < 3:
            time.sleep(5)
            continue

        # Use all candles up to previous one to find last bearish
        df_slice = df.iloc[:-1]
        last_spike = find_last_bearish_spike(df_slice)
        current_row = df.iloc[-1]
        prev_row = df.iloc[-2]
        now = current_row.name

        # Entry: cross above and close above last bearish candle open, and previous high did not cross
        if last_spike and not open_trade:
            prev_spike_open = last_spike['Spike_Open']
            prev_spike_close = last_spike['Spike_Close']
            prev_spike_time = last_spike['Datetime']
            ads_pips = 0.5 * abs(prev_spike_open - prev_spike_close)
            
            if ads_pips == 0:
                time.sleep(2)
                continue
            if now - prev_spike_time > timedelta(seconds=20):
                crossed = (current_row['high'] > prev_spike_open)
                closed_above = current_row['close'] > prev_spike_open
                if crossed and closed_above:
                    entry_price = get_current_price()
                    entry_time = datetime.now()
                    tp_price = entry_price + ads_pips
                    ticket = open_long(lot_size, entry_price, tp=tp_price)
                    if ticket:
                        open_trade = {
                            'ticket': ticket,
                            'entry_time': entry_time,
                            'entry_price': entry_price,
                            'ads_pips': ads_pips,
                            'prev_candle_low': prev_row['low'],
                            'entry_idx': now
                        }
                        print(f"Opened LONG at {entry_price} (ticket {ticket}) after cross above spike open at {prev_spike_open}, TP set at {tp_price}")
                    else:
                        print(f"Failed to open LONG at {entry_price} after cross above spike open at {prev_spike_open}")
                        time.sleep(2)
                        continue

        # Exit condition: close if low below previous candle low or price moves by ads_pips in your favor
        if open_trade:
            df_check = get_5min_ohlc(symbol, 3)
            if df_check is None or len(df_check) < 2:
                time.sleep(2)
                continue
            current_row = df_check.iloc[-1]
            prev_row = df_check.iloc[-2]
            exit_trade = False
            exit_reason = ""
            if current_row['low'] < open_trade['prev_candle_low']:
                exit_trade = True
                exit_reason = "Low crossed below previous candle low"
            # else:
            #     price_move = current_row['close'] - open_trade['entry_price']
            #     if price_move >= open_trade['ads_pips']:
            #         exit_trade = True
            #         exit_reason = f"ADS pip target reached ({open_trade['ads_pips']:.2f})"
            if exit_trade:
                ticket = open_trade['ticket']
                exit_price = get_current_price()
                closed = close_position(ticket)
                time.sleep(2)
                pnl = get_closed_pnl(ticket)
                taxed_pnl = pnl - 0.1  # Apply tax (for tracking)
                trade_history.append({
                    'entry_time': open_trade['entry_time'].strftime("%Y-%m-%d %H:%M:%S"),
                    'entry_price': open_trade['entry_price'],
                    'exit_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'exit_price': exit_price,
                    'pnl': pnl,
                    'taxed_pnl': taxed_pnl,
                    'exit_reason': exit_reason
                })
                print(f"Exit: {exit_reason}")
                open_trade = None
                continue

        print_trade_table()
        time.sleep(2)
